# Repository Guidelines

## Project Structure & Module Organization
- `src/`: React + TypeScript frontend. Entry: `src/main.tsx`; app shell: `src/App.tsx`. Components in `src/components/` (PascalCase, e.g., `StatsCards.tsx`); utilities in `src/lib/`.
- `convex/`: Convex backend. Routes: `convex/router.ts`; HTTP: `convex/http.ts`; schema: `convex/schema.ts`; auth: `convex/auth.ts`. Use validators from `convex/values` and new function syntax. Always declare `args` and `returns`; use `query`, `mutation`, `action`, or `internal*` variants.
- `docs/`: Operational docs (`SECURITY_SETUP.md`, `DEPLOYMENT_CHECKLIST.md`, `CONVEX_RULES.md`).
- `dist/`: Vite build output — do not edit by hand.
- Config: `vite.config.ts`, `tailwind.config.js`, `eslint.config.js`, `tsconfig*.json`, `.env.local` (ignored), `convex.env`.

## Build, Test, and Development Commands
- `npm install`: Install dependencies.
- `npm run dev`: Run frontend (Vite `--open`) and backend (`convex dev`) together.
- `npm run build`: Production build via Vite into `dist/`.
- `npm run lint`: Type-checks frontend and Convex code, runs a one-off Convex compile, then builds. Use locally and in CI.

## Coding Style & Naming Conventions
- **Language**: TypeScript, React 19, Convex.
- **Indentation**: 2 spaces; **quotes**: single; **trailing commas**: where valid.
- **Components**: PascalCase files in `src/components/`; **helpers**: camelCase in `src/lib/` (e.g., `utils.ts`).
- **Styling**: TailwindCSS; use `clsx` + `tailwind-merge` for conditional classes.
- **Lint/Format**: ESLint + Prettier. Run `npm run lint` before pushing.

## Testing Guidelines
- Automated tests are not yet configured. Until added, rely on `npm run lint` for type safety and sanity checks.
- If introducing tests, prefer Vitest + React Testing Library for `src/` and focused query/mutation tests for `convex/`. Name tests `*.test.ts(x)` near sources or under `tests/`. Run with `vitest` or `npm test` once configured.

## Commit & Pull Request Guidelines
- **Commits**: Conventional Commits (e.g., `feat: add webhook logs panel`, `fix(convex): validate telegram payload`).
- **PRs**: Include a concise description, linked issues, screenshots for UI changes, notes on schema or env changes (e.g., Convex env vars), and manual test steps.

## Security & Configuration Tips
- Never commit secrets. Keep `.env.local` untracked; configure Convex env vars in the dashboard and declare them in `convex.env`.
- Review `docs/SECURITY_SETUP.md`, `docs/DEPLOYMENT_CHECKLIST.md`, and `docs/CONVEX_RULES.md` before deploying (keys, webhook secrets, limits, rate limiting).

