import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  CheckCircle2, 
  Circle, 
  ArrowRight, 
  ArrowLeft, 
  <PERSON>py, 
  ExternalLink,
  <PERSON>t,
  Key,
  Webhook,
  TestTube,
  Sparkles
} from "lucide-react";
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  component: React.ReactNode;
  isComplete: boolean;
  isOptional?: boolean;
}

export function OnboardingWizard() {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({
    TELEGRAM_BOT_TOKEN: "",
    LINEAR_API_KEY: "",
    LINEAR_TEAM_ID: "",
    OPENROUTER_API_KEY: "",
  });
  const [webhookSecret, setWebhookSecret] = useState("");
  const [isTestingWebhook, setIsTestingWebhook] = useState(false);
  
  const settings = useQuery(api.settings.getAllSettings);
  const setSetting = useMutation(api.settings.setSetting);
  const generateWebhookSecret = useAction(api.admin.generateWebhookSecret);
  const buildWebhookUrl = useQuery(api.webhooks.buildWebhookUrl, { 
    secret: webhookSecret || undefined 
  });
  const testWebhook = useAction(api.webhooks.testWebhook);
  const validateConfiguration = useAction(api.settings.validateConfiguration);

  // Update form data when settings load
  useEffect(() => {
    if (settings) {
      const settingsMap = settings.reduce((acc, setting) => {
        acc[setting.key] = setting.value;
        return acc;
      }, {} as Record<string, string>);
      
      setFormData(prev => ({
        ...prev,
        ...settingsMap,
      }));
    }
  }, [settings]);

  const handleGenerateSecret = async () => {
    try {
      const secret = await generateWebhookSecret();
      setWebhookSecret(secret);
      toast.success("Webhook secret generated!");
    } catch (error) {
      toast.error("Failed to generate webhook secret");
    }
  };

  const handleSaveSettings = async () => {
    try {
      await Promise.all(
        Object.entries(formData).map(([key, value]) =>
          setSetting({ key, value })
        )
      );
      toast.success("Settings saved!");
    } catch (error) {
      toast.error("Failed to save settings");
    }
  };

  const handleTestWebhook = async () => {
    if (!formData.TELEGRAM_BOT_TOKEN || !buildWebhookUrl) {
      toast.error("Bot token and webhook URL required");
      return;
    }

    setIsTestingWebhook(true);
    try {
      const result = await testWebhook({
        botToken: formData.TELEGRAM_BOT_TOKEN,
        webhookUrl: buildWebhookUrl,
      });

      if (result.success) {
        toast.success("Webhook configured successfully!");
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error("Failed to test webhook");
    } finally {
      setIsTestingWebhook(false);
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard!`);
  };

  const steps: OnboardingStep[] = [
    {
      id: "welcome",
      title: "Welcome to BuddyTasks",
      description: "Let's set up your Telegram to Linear automation in just a few steps",
      icon: <Sparkles className="w-6 h-6" />,
      isComplete: true,
      component: (
        <div className="text-center space-y-6">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="w-24 h-24 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center"
          >
            <Bot className="w-12 h-12 text-white" />
          </motion.div>
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-3">
              Transform Telegram Messages into Linear Tasks
            </h3>
            <p className="text-gray-600 text-lg">
              Automatically convert your Telegram messages into structured Linear tasks using AI. 
              This setup will take about 5 minutes.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <Bot className="w-8 h-8 text-blue-600 mb-2" />
              <h4 className="font-semibold text-gray-900">Telegram Bot</h4>
              <p className="text-sm text-gray-600">Create and configure your bot</p>
            </div>
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <Key className="w-8 h-8 text-green-600 mb-2" />
              <h4 className="font-semibold text-gray-900">API Keys</h4>
              <p className="text-sm text-gray-600">Connect Linear and AI services</p>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
              <Webhook className="w-8 h-8 text-purple-600 mb-2" />
              <h4 className="font-semibold text-gray-900">Webhook</h4>
              <p className="text-sm text-gray-600">Enable real-time message processing</p>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: "telegram-bot",
      title: "Create Telegram Bot",
      description: "Create a new bot with @BotFather and get your bot token",
      icon: <Bot className="w-6 h-6" />,
      isComplete: !!formData.TELEGRAM_BOT_TOKEN,
      component: (
        <div className="space-y-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-semibold text-blue-900 mb-2">Step-by-step guide:</h4>
            <ol className="list-decimal list-inside space-y-2 text-sm text-blue-800">
              <li>Open Telegram and search for <strong>@BotFather</strong></li>
              <li>Send the command <code>/newbot</code></li>
              <li>Choose a name for your bot (e.g., "My Task Bot")</li>
              <li>Choose a username ending in "bot" (e.g., "mytask_bot")</li>
              <li>Copy the bot token from the response</li>
            </ol>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Telegram Bot Token
            </label>
            <div className="flex gap-2">
              <input
                type="password"
                value={formData.TELEGRAM_BOT_TOKEN}
                onChange={(e) => setFormData(prev => ({ ...prev, TELEGRAM_BOT_TOKEN: e.target.value }))}
                placeholder="123456789:ABCdefGHIjklMNOpqrsTUVwxyz"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                onClick={() => window.open('https://t.me/BotFather', '_blank')}
                className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center gap-2"
              >
                <ExternalLink className="w-4 h-4" />
                Open @BotFather
              </button>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: "api-keys",
      title: "Configure API Keys",
      description: "Add your Linear and OpenRouter API keys for full functionality",
      icon: <Key className="w-6 h-6" />,
      isComplete: !!(formData.LINEAR_API_KEY && formData.OPENROUTER_API_KEY),
      component: (
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Linear API Key *
            </label>
            <div className="flex gap-2">
              <input
                type="password"
                value={formData.LINEAR_API_KEY}
                onChange={(e) => setFormData(prev => ({ ...prev, LINEAR_API_KEY: e.target.value }))}
                placeholder="lin_api_..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                onClick={() => window.open('https://linear.app/settings/api', '_blank')}
                className="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center gap-2"
              >
                <ExternalLink className="w-4 h-4" />
                Get Key
              </button>
            </div>
            <p className="text-sm text-gray-500 mt-1">
              Required to create tasks in Linear
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Linear Team ID *
            </label>
            <input
              type="text"
              value={formData.LINEAR_TEAM_ID}
              onChange={(e) => setFormData(prev => ({ ...prev, LINEAR_TEAM_ID: e.target.value }))}
              placeholder="team-id-here"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <p className="text-sm text-gray-500 mt-1">
              Find this in your Linear team settings
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              OpenRouter API Key *
            </label>
            <div className="flex gap-2">
              <input
                type="password"
                value={formData.OPENROUTER_API_KEY}
                onChange={(e) => setFormData(prev => ({ ...prev, OPENROUTER_API_KEY: e.target.value }))}
                placeholder="sk-or-..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                onClick={() => window.open('https://openrouter.ai/keys', '_blank')}
                className="px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 flex items-center gap-2"
              >
                <ExternalLink className="w-4 h-4" />
                Get Key
              </button>
            </div>
            <p className="text-sm text-gray-500 mt-1">
              Required for AI-powered task extraction
            </p>
          </div>
        </div>
      ),
    },
    {
      id: "webhook",
      title: "Setup Webhook",
      description: "Generate a secure webhook and configure your bot",
      icon: <Webhook className="w-6 h-6" />,
      isComplete: !!webhookSecret,
      component: (
        <div className="space-y-6">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h4 className="font-semibold text-yellow-900 mb-2">🔐 Webhook Security</h4>
            <p className="text-sm text-yellow-800">
              We'll generate a secure secret for your webhook to ensure only Telegram can send messages to your bot.
            </p>
          </div>

          <div className="space-y-4">
            <button
              onClick={handleGenerateSecret}
              className="w-full px-4 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center justify-center gap-2 font-medium"
            >
              <Key className="w-5 h-5" />
              Generate Webhook Secret
            </button>

            {webhookSecret && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-4"
              >
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-green-900">✅ Secret Generated</h4>
                    <button
                      onClick={() => copyToClipboard(webhookSecret, "Webhook secret")}
                      className="text-green-700 hover:text-green-900 text-sm font-medium flex items-center gap-1"
                    >
                      <Copy className="w-4 h-4" />
                      Copy
                    </button>
                  </div>
                  <code className="text-sm bg-white p-2 rounded border block break-all">
                    {webhookSecret}
                  </code>
                </div>

                {buildWebhookUrl && (
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold text-blue-900">🔗 Your Webhook URL</h4>
                      <button
                        onClick={() => copyToClipboard(buildWebhookUrl, "Webhook URL")}
                        className="text-blue-700 hover:text-blue-900 text-sm font-medium flex items-center gap-1"
                      >
                        <Copy className="w-4 h-4" />
                        Copy
                      </button>
                    </div>
                    <code className="text-sm bg-white p-2 rounded border block break-all">
                      {buildWebhookUrl}
                    </code>
                    <p className="text-sm text-blue-700 mt-2">
                      ℹ️ This URL uses your <strong>Convex backend</strong>, not your frontend domain.
                    </p>
                  </div>
                )}
              </motion.div>
            )}
          </div>
        </div>
      ),
    },
    {
      id: "test",
      title: "Test Integration",
      description: "Save your settings and test the webhook connection",
      icon: <TestTube className="w-6 h-6" />,
      isComplete: false,
      component: (
        <div className="space-y-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-semibold text-blue-900 mb-2">📋 Final Steps</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm text-blue-800">
              <li>Save your configuration</li>
              <li>Test the webhook connection</li>
              <li>Send a test message to your bot</li>
            </ol>
          </div>

          <div className="space-y-4">
            <button
              onClick={handleSaveSettings}
              className="w-full px-4 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium"
            >
              💾 Save All Settings
            </button>

            <button
              onClick={handleTestWebhook}
              disabled={!formData.TELEGRAM_BOT_TOKEN || !buildWebhookUrl || isTestingWebhook}
              className="w-full px-4 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium flex items-center justify-center gap-2"
            >
              {isTestingWebhook ? (
                <>
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                  />
                  Testing Webhook...
                </>
              ) : (
                <>
                  <TestTube className="w-5 h-5" />
                  Test Webhook Connection
                </>
              )}
            </button>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h4 className="font-semibold text-green-900 mb-2">🎉 You're Almost Done!</h4>
            <p className="text-sm text-green-800 mb-3">
              After testing, try sending a message to your bot like:
            </p>
            <code className="text-sm bg-white p-2 rounded border block">
              "Create a task to fix the login bug in the mobile app"
            </code>
          </div>
        </div>
      ),
    },
  ];

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const currentStepData = steps[currentStep];
  const progress = ((currentStep + 1) / steps.length) * 100;

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
        {/* Progress Bar */}
        <div className="h-2 bg-gray-200">
          <motion.div
            className="h-full bg-gradient-to-r from-blue-500 to-purple-600"
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        </div>

        {/* Header */}
        <div className="px-8 py-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <motion.div
                key={currentStep}
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white"
              >
                {currentStepData.icon}
              </motion.div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900">
                  {currentStepData.title}
                </h2>
                <p className="text-gray-600">
                  {currentStepData.description}
                </p>
              </div>
            </div>
            <div className="text-sm font-medium text-gray-500">
              {currentStep + 1} of {steps.length}
            </div>
          </div>
        </div>

        {/* Steps Navigation */}
        <div className="px-8 py-4 bg-gray-50 border-b border-gray-100">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <motion.button
                  onClick={() => setCurrentStep(index)}
                  className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors ${
                    index === currentStep
                      ? "bg-blue-600 text-white"
                      : step.isComplete
                      ? "bg-green-500 text-white"
                      : "bg-gray-300 text-gray-600"
                  }`}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {step.isComplete && index !== currentStep ? (
                    <CheckCircle2 className="w-5 h-5" />
                  ) : (
                    <span className="text-sm font-medium">{index + 1}</span>
                  )}
                </motion.button>
                {index < steps.length - 1 && (
                  <div className={`w-12 h-0.5 mx-2 ${
                    step.isComplete ? "bg-green-500" : "bg-gray-300"
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="px-8 py-8">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              {currentStepData.component}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Navigation */}
        <div className="px-8 py-6 bg-gray-50 border-t border-gray-100 flex justify-between">
          <button
            onClick={prevStep}
            disabled={currentStep === 0}
            className="px-4 py-2 text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Previous
          </button>
          
          <div className="flex gap-2">
            {currentStep < steps.length - 1 && (
              <button
                onClick={nextStep}
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center gap-2"
              >
                Next
                <ArrowRight className="w-4 h-4" />
              </button>
            )}
            {currentStep === steps.length - 1 && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-2 bg-gradient-to-r from-green-500 to-blue-600 text-white rounded-md font-medium flex items-center gap-2"
              >
                <Sparkles className="w-4 h-4" />
                Complete Setup
              </motion.button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}