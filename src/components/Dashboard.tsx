import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { StatsCards } from "./StatsCards";
import { TasksList } from "./TasksList";
import { MessagesList } from "./MessagesList";
import { WebhookLogs } from "./WebhookLogs";
import { ConnectionStatus } from "./ConnectionStatus";
import { useState } from "react";
import { motion } from "framer-motion";
import { TrendingUp, Activity, Zap } from "lucide-react";

export function Dashboard() {
  const [activeView, setActiveView] = useState<"tasks" | "messages" | "logs">("tasks");
  const messageStats = useQuery(api.telegram.getMessageStats);
  const taskStats = useQuery(api.tasks.getTaskStats);
  const webhookStats = useQuery(api.webhooks.getWebhookStats);

  const tabs = [
    {
      id: "tasks",
      label: "Tasks",
      icon: "✅",
      count: taskStats?.total || 0,
    },
    {
      id: "messages",
      label: "Messages",
      icon: "💬",
      count: messageStats?.total || 0,
    },
    {
      id: "logs",
      label: "Webhook Logs",
      icon: "🔗",
      count: webhookStats?.total || 0,
    },
  ];

  return (
    <div className="space-y-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
            Dashboard
          </h2>
          <p className="text-gray-600 text-lg flex items-center gap-2">
            <Activity className="w-5 h-5" />
            Monitor your Telegram to Linear integration in real-time
          </p>
        </div>
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2 }}
          className="hidden md:flex items-center gap-2 px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full border border-gray-200 shadow-sm"
        >
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          <span className="text-sm font-medium text-gray-700">Live</span>
        </motion.div>
      </motion.div>

      {/* Connection Status and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <StatsCards />
        </div>
        <div className="space-y-4">
          <ConnectionStatus />
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="p-4 bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl border border-blue-200"
          >
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="w-5 h-5 text-blue-600" />
              <h3 className="font-semibold text-blue-900">Quick Tips</h3>
            </div>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Send "Create task: Fix login bug" to your bot</li>
              <li>• Messages are processed automatically</li>
              <li>• Check Linear for created tasks</li>
            </ul>
          </motion.div>
        </div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200 overflow-hidden"
      >
        <div className="border-b border-gray-200/50">
          <nav className="flex space-x-2 px-6 py-2">
            {tabs.map((tab) => (
              <motion.button
                key={tab.id}
                onClick={() => setActiveView(tab.id as any)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className={`py-3 px-4 rounded-lg font-medium text-sm transition-all duration-200 flex items-center gap-2 ${
                  activeView === tab.id
                    ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg"
                    : "text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                }`}
              >
                <span className="text-base">{tab.icon}</span>
                <span>{tab.label}</span>
                {tab.count > 0 && (
                  <motion.span
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className={`px-2 py-1 rounded-full text-xs font-medium ${
                      activeView === tab.id
                        ? "bg-white/20 text-white"
                        : "bg-gray-200 text-gray-600"
                    }`}
                  >
                    {tab.count}
                  </motion.span>
                )}
              </motion.button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          <motion.div
            key={activeView}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            {activeView === "tasks" && <TasksList />}
            {activeView === "messages" && <MessagesList />}
            {activeView === "logs" && <WebhookLogs />}
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
}
