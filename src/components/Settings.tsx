import { useState, useEffect } from "react";
import { useMutation, useQuery, useAction } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";
import { KeyStatusCard } from "./KeyStatusCard";

export function Settings() {
  const settings = useQuery(api.settings.getAllSettings);
  const setSetting = useMutation(api.settings.setSetting);
  const testWebhook = useAction(api.webhooks.testWebhook);
  const getWebhookInfo = useAction(api.webhooks.getWebhookInfo);
  const diagnoseWebhookSetup = useAction(api.webhooks.diagnoseWebhookSetup);
  const testWebhookEndpoint = useAction(api.webhooks.testWebhookEndpoint);
  const validateConfiguration = useAction(api.settings.validateConfiguration);
  const generateWebhookSecret = useAction(api.admin.generateWebhookSecret);
  const generateWebhookSecretPreview = useAction(api.admin.generateWebhookSecretPreview);
  const buildWebhookUrl = useQuery(api.webhooks.buildWebhookUrl, {});
  
  const [formData, setFormData] = useState({
    OPENROUTER_API_KEY: "",
    LINEAR_API_KEY: "",
    LINEAR_TEAM_ID: "",
    TELEGRAM_BOT_TOKEN: "",
  });

  const [loading, setLoading] = useState(false);
  const [validating, setValidating] = useState(false);
  const [webhookTesting, setWebhookTesting] = useState(false);
  const [diagnosing, setDiagnosing] = useState(false);
  const [endpointTesting, setEndpointTesting] = useState(false);
  const [generatingSecret, setGeneratingSecret] = useState(false);
  const [webhookInfo, setWebhookInfo] = useState<any>(null);
  const [diagnosisResults, setDiagnosisResults] = useState<any>(null);
  const [endpointTestResults, setEndpointTestResults] = useState<any>(null);
  const [validationResults, setValidationResults] = useState<any>(null);
  const [generatedSecret, setGeneratedSecret] = useState<string | null>(null);
  const [lastWebhookError, setLastWebhookError] = useState<any>(null);

  // Update form data when settings load
  useEffect(() => {
    if (settings) {
      const settingsMap = settings.reduce((acc, setting) => {
        acc[setting.key] = setting.value;
        return acc;
      }, {} as Record<string, string>);
      
      setFormData(prev => ({
        ...prev,
        ...settingsMap,
      }));
    }
  }, [settings]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Save all settings
      await Promise.all(
        Object.entries(formData).map(([key, value]) =>
          setSetting({ key, value })
        )
      );

      toast.success("Settings saved successfully! 🔐 Data is isolated to your account.");
    } catch (error) {
      toast.error("Failed to save settings");
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (key: string, value: string) => {
    setFormData(prev => ({ ...prev, [key]: value }));
  };

  const handleTestWebhook = async () => {
    console.log("🚀 Frontend Debug - Starting webhook setup");
    console.log("🚀 Frontend Debug - Bot token (first 10 chars):", formData.TELEGRAM_BOT_TOKEN?.substring(0, 10) + "...");
    console.log("🚀 Frontend Debug - Webhook URL:", buildWebhookUrl);
    
    if (!formData.TELEGRAM_BOT_TOKEN) {
      toast.error("Please enter a Telegram bot token first");
      return;
    }

    if (!buildWebhookUrl) {
      console.error("❌ Frontend Debug - buildWebhookUrl is null/undefined");
      toast.error("Webhook URL not available. Please try again.");
      return;
    }

    // Clear previous error state
    setLastWebhookError(null);

    setWebhookTesting(true);
    try {
      console.log("📡 Frontend Debug - Calling testWebhook action...");
      const result = await testWebhook({
        botToken: formData.TELEGRAM_BOT_TOKEN,
        webhookUrl: buildWebhookUrl,
      });

      console.log("📡 Frontend Debug - testWebhook result:", result);
      
      // Store debug information for display
      if (result.debug) {
        console.log("🔍 Frontend Debug - Detailed debug info:", result.debug);
        setLastWebhookError(result.debug);
      }

      if (result.success) {
        console.log("✅ Frontend Debug - Webhook setup successful!");
        toast.success(result.message);
        await handleGetWebhookInfo();
        setLastWebhookError(null); // Clear error on success
      } else {
        console.error("❌ Frontend Debug - Webhook setup failed:", result.message);
        toast.error(`Setup failed: ${result.message}`);
        
        // Show more detailed error if available
        if (result.debug?.error) {
          console.error("❌ Frontend Debug - Additional error details:", result.debug.error);
        }
      }
    } catch (error) {
      console.error("❌ Frontend Debug - Exception during webhook test:", error);
      toast.error(`Webhook setup failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setLastWebhookError({ error: error instanceof Error ? error.message : 'Unknown error' });
    } finally {
      setWebhookTesting(false);
      console.log("🏁 Frontend Debug - Webhook setup completed");
    }
  };

  const handleGetWebhookInfo = async () => {
    if (!formData.TELEGRAM_BOT_TOKEN) return;

    try {
      const result = await getWebhookInfo({
        botToken: formData.TELEGRAM_BOT_TOKEN,
      });

      if (result.success) {
        setWebhookInfo(result.data);
      }
    } catch (error) {
      console.error("Failed to get webhook info:", error);
    }
  };

  const handleDiagnoseWebhook = async () => {
    console.log("🔍 Frontend Debug - Starting webhook diagnosis");
    
    if (!formData.TELEGRAM_BOT_TOKEN) {
      toast.error("Please enter a Telegram bot token first");
      return;
    }

    setDiagnosing(true);
    setDiagnosisResults(null);
    
    try {
      const diagnosis = await diagnoseWebhookSetup({
        botToken: formData.TELEGRAM_BOT_TOKEN,
      });

      console.log("🔍 Frontend Debug - Diagnosis results:", diagnosis);
      setDiagnosisResults(diagnosis);
      
      // Show summary in toast
      const failCount = Object.values(diagnosis).filter((item: any) => 
        typeof item === 'object' && item !== null && 'status' in item && item.status.includes('❌')
      ).length;
      
      if (failCount === 0) {
        toast.success("✅ Diagnosis complete - All checks passed!");
      } else {
        toast.warning(`⚠️ Diagnosis complete - ${failCount} issues found`);
      }
      
    } catch (error) {
      console.error("❌ Frontend Debug - Diagnosis failed:", error);
      toast.error("Failed to run diagnosis");
    } finally {
      setDiagnosing(false);
    }
  };

  const handleTestEndpoint = async () => {
    console.log("🌐 Frontend Debug - Testing webhook endpoint accessibility");
    
    setEndpointTesting(true);
    setEndpointTestResults(null);
    
    try {
      const result = await testWebhookEndpoint();
      console.log("🌐 Frontend Debug - Endpoint test results:", result);
      setEndpointTestResults(result);
      
      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.message);
      }
      
    } catch (error) {
      console.error("❌ Frontend Debug - Endpoint test failed:", error);
      toast.error("Failed to test endpoint");
    } finally {
      setEndpointTesting(false);
    }
  };

  const handleValidateConfiguration = async () => {
    setValidating(true);
    try {
      const results = await validateConfiguration();
      setValidationResults(results);
      
      const keyValues = Object.values(results.keys || {});
      const allValid = keyValues.every((r: any) => r.valid);
      const someValid = keyValues.some((r: any) => r.valid);
      
      if (allValid && keyValues.length > 0) {
        toast.success("🟢 All configurations are valid!");
      } else if (someValid) {
        toast.warning("🟡 Some configurations need attention");
      } else {
        toast.error("🔴 Configuration setup required");
      }
    } catch (error) {
      toast.error("Failed to validate configuration");
      console.error("Validation error:", error);
    } finally {
      setValidating(false);
    }
  };

  const handleGenerateWebhookSecret = async (preview: boolean = false) => {
    setGeneratingSecret(true);
    try {
      let secret: string;
      if (preview) {
        secret = await generateWebhookSecretPreview();
        setGeneratedSecret(secret);
        toast.success("🔑 Webhook secret generated (preview only)");
      } else {
        secret = await generateWebhookSecret();
        setGeneratedSecret(secret);
        
        // Refresh validation results to show the new secret
        await handleValidateConfiguration();
        
        toast.success("🔐 Webhook secret generated and saved securely!");
      }
    } catch (error) {
      toast.error("Failed to generate webhook secret");
      console.error("Secret generation error:", error);
    } finally {
      setGeneratingSecret(false);
    }
  };

  // Use the dynamic webhook URL from Convex instead of window.location.origin
  const webhookUrl = buildWebhookUrl || "Loading webhook URL...";

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Settings</h2>
        <p className="text-gray-600">
          Configure your API keys and integration settings. 🔐 Your data is isolated and secure.
        </p>
      </div>

      {/* Security Notice */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <div className="text-green-600 text-xl">🔐</div>
          <div>
            <h3 className="font-medium text-green-900 mb-1">Security Features Enabled</h3>
            <ul className="text-sm text-green-800 space-y-1">
              <li>• <strong>User Isolation:</strong> Your settings are private and isolated from other users</li>
              <li>• <strong>Secure Storage:</strong> All data is tied to your authenticated account</li>
              <li>• <strong>Private Processing:</strong> Tasks and messages are only visible to you</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Configuration Status */}
      {validationResults && (
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Configuration Status</h3>
              <p className="text-sm text-gray-600 mt-1">
                Individual validation status for each API key and setting
              </p>
            </div>
            <div className="flex items-center gap-4">
              {/* Overall Status Badge */}
              {validationResults.overallStatus && (
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                  validationResults.overallStatus === 'complete' 
                    ? 'bg-green-100 text-green-800' 
                    : validationResults.overallStatus === 'partial'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {validationResults.overallStatus === 'complete' ? '🟢 Complete' :
                   validationResults.overallStatus === 'partial' ? '🟡 Partial' :
                   '🔴 Needs Attention'}
                </div>
              )}
              <button
                onClick={handleValidateConfiguration}
                disabled={validating}
                className="px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
              >
                <svg className={`w-4 h-4 ${validating ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                {validating ? "Validating..." : "Validate All"}
              </button>
            </div>
          </div>
          
          {/* Encryption Status Banner */}
          {validationResults.encryptionStatus && (
            <div className={`mb-6 p-4 rounded-lg border ${
              validationResults.encryptionStatus.enabled 
                ? 'bg-green-50 border-green-200' 
                : 'bg-yellow-50 border-yellow-200'
            }`}>
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-full ${
                  validationResults.encryptionStatus.enabled 
                    ? 'bg-green-100 text-green-600' 
                    : 'bg-yellow-100 text-yellow-600'
                }`}>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">
                    Encryption {validationResults.encryptionStatus.enabled ? 'Enabled' : 'Disabled'}
                  </h4>
                  <p className="text-sm text-gray-600">
                    {validationResults.encryptionStatus.enabled 
                      ? 'Sensitive data is encrypted with AES-256-GCM' 
                      : 'Enable encryption by setting ENCRYPTION_MASTER_KEY environment variable'}
                  </p>
                </div>
              </div>
            </div>
          )}
          
          {/* Individual Key Status Cards */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {validationResults.keys && Object.entries(validationResults.keys).map(([key, status]: [string, any]) => {
              const displayNames: Record<string, string> = {
                'OPENROUTER_API_KEY': 'OpenRouter API Key',
                'LINEAR_API_KEY': 'Linear API Key', 
                'LINEAR_TEAM_ID': 'Linear Team ID',
                'TELEGRAM_BOT_TOKEN': 'Telegram Bot Token',
                'TELEGRAM_WEBHOOK_SECRET': 'Webhook Secret'
              };
              
              return (
                <KeyStatusCard
                  key={key}
                  keyName={key}
                  displayName={displayNames[key] || key}
                  status={status}
                  onRevalidate={() => handleValidateConfiguration()}
                  isValidating={validating}
                />
              );
            })}
          </div>
        </div>
      )}

      {/* Webhook Security Section */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Webhook Security</h3>
            <p className="text-sm text-gray-600 mt-1">
              Generate and manage secure webhook secrets for Telegram integration
            </p>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => handleGenerateWebhookSecret(true)}
              disabled={generatingSecret}
              className="px-3 py-2 text-sm bg-gray-600 text-white rounded hover:bg-gray-700 disabled:opacity-50 flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              {generatingSecret ? "Generating..." : "Preview"}
            </button>
            <button
              onClick={() => handleGenerateWebhookSecret(false)}
              disabled={generatingSecret}
              className="px-4 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
            >
              <svg className={`w-4 h-4 ${generatingSecret ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              {generatingSecret ? "Generating..." : "Generate & Save"}
            </button>
          </div>
        </div>

        {/* Generated Secret Display */}
        {generatedSecret && (
          <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-blue-900">🔐 Generated Webhook Secret</h4>
              <button
                onClick={() => navigator.clipboard.writeText(generatedSecret)}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                Copy Secret
              </button>
            </div>
            <div className="bg-white p-3 rounded border font-mono text-sm break-all">
              {generatedSecret}
            </div>
            <p className="text-sm text-blue-700 mt-2">
              ⚠️ Save this secret securely. Telegram will send it in the
              <code className="bg-blue-100 px-1 rounded text-xs ml-1">X-Telegram-Bot-Api-Secret-Token</code>
              header. The webhook URL does not include the secret.
            </p>
          </div>
        )}

        {/* Security Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <h4 className="font-medium text-green-900">Security Features</h4>
            </div>
            <ul className="text-sm text-green-800 space-y-1">
              <li>• AES-256-GCM encryption</li>
              <li>• User-specific isolation</li>
              <li>• Secure random generation</li>
              <li>• Automatic expiration</li>
            </ul>
          </div>
          
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <h4 className="font-medium text-yellow-900">Best Practices</h4>
            </div>
            <ul className="text-sm text-yellow-800 space-y-1">
              <li>• Regenerate periodically</li>
              <li>• Use HTTPS URLs only</li>
              <li>• Keep secrets private</li>
              <li>• Monitor webhook logs</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Webhook Configuration */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Webhook Configuration</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Webhook URL
            </label>
            <div className="flex gap-2">
              <input
                type="text"
                value={webhookUrl}
                readOnly
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-600"
              />
              <button
                onClick={() => navigator.clipboard.writeText(webhookUrl)}
                className="px-3 py-2 text-sm bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                Copy
              </button>
            </div>
          </div>

          <div className="flex gap-2 flex-wrap">
            <button
              onClick={handleTestWebhook}
              disabled={webhookTesting || !formData.TELEGRAM_BOT_TOKEN}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {webhookTesting ? "Setting up..." : "Setup Webhook"}
            </button>
            <button
              onClick={handleGetWebhookInfo}
              disabled={!formData.TELEGRAM_BOT_TOKEN}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 disabled:opacity-50"
            >
              Get Info
            </button>
            <button
              onClick={handleTestEndpoint}
              disabled={endpointTesting}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
            >
              {endpointTesting ? "Testing..." : "🌐 Test URL"}
            </button>
            <button
              onClick={handleDiagnoseWebhook}
              disabled={diagnosing || !formData.TELEGRAM_BOT_TOKEN}
              className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
            >
              {diagnosing ? "Diagnosing..." : "🔍 Diagnose"}
            </button>
          </div>

          {webhookInfo && (
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Current Webhook Status</h4>
              <div className="text-sm space-y-1">
                <p><span className="font-medium">URL:</span> {webhookInfo.url || "Not set"}</p>
                <p><span className="font-medium">Pending Updates:</span> {webhookInfo.pending_update_count}</p>
                <p><span className="font-medium">Last Error:</span> {webhookInfo.last_error_message || "None"}</p>
                {webhookInfo.last_error_date && (
                  <p><span className="font-medium">Last Error Date:</span> {new Date(webhookInfo.last_error_date * 1000).toLocaleString()}</p>
                )}
              </div>
            </div>
          )}

          {/* Endpoint Test Results */}
          {endpointTestResults && (
            <div className={`mt-4 p-4 rounded-lg border ${
              endpointTestResults.success 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <h4 className={`font-medium mb-3 ${
                endpointTestResults.success ? 'text-green-900' : 'text-red-900'
              }`}>
                🌐 Webhook Endpoint Test Results
              </h4>
              <div className="space-y-2 text-sm">
                <p><span className="font-medium">Status:</span> {endpointTestResults.message}</p>
                <p><span className="font-medium">HTTP Status:</span> {endpointTestResults.status}</p>
                <p><span className="font-medium">URL:</span> {endpointTestResults.url}</p>
                {endpointTestResults.body && (
                  <div className="mt-2">
                    <p className="font-medium">Response Body:</p>
                    <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto max-h-32">
                      {endpointTestResults.body}
                    </pre>
                  </div>
                )}
                {Object.keys(endpointTestResults.headers).length > 0 && (
                  <details className="mt-2">
                    <summary className="font-medium cursor-pointer">Response Headers</summary>
                    <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                      {JSON.stringify(endpointTestResults.headers, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            </div>
          )}

          {/* Diagnosis Results */}
          {diagnosisResults && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <h4 className="font-medium text-blue-900 mb-3">🔍 Webhook Setup Diagnosis</h4>
              <div className="space-y-3 text-sm">
                <div className={`p-2 rounded ${diagnosisResults.authentication.status.includes('✅') ? 'bg-green-100' : 'bg-red-100'}`}>
                  <span className="font-medium">Authentication:</span> {diagnosisResults.authentication.status} - {diagnosisResults.authentication.details}
                </div>
                <div className={`p-2 rounded ${diagnosisResults.webhookSecret.status.includes('✅') ? 'bg-green-100' : diagnosisResults.webhookSecret.status.includes('⚠️') ? 'bg-yellow-100' : 'bg-red-100'}`}>
                  <span className="font-medium">Webhook Secret:</span> {diagnosisResults.webhookSecret.status} - {diagnosisResults.webhookSecret.details}
                </div>
                <div className={`p-2 rounded ${diagnosisResults.webhookUrl.status.includes('✅') ? 'bg-green-100' : 'bg-red-100'}`}>
                  <span className="font-medium">Webhook URL:</span> {diagnosisResults.webhookUrl.status} - {diagnosisResults.webhookUrl.details}
                  {diagnosisResults.webhookUrl.url && (
                    <p className="text-xs text-gray-600 mt-1">URL: {diagnosisResults.webhookUrl.url}</p>
                  )}
                </div>
                <div className={`p-2 rounded ${diagnosisResults.telegramBot.status.includes('✅') ? 'bg-green-100' : 'bg-red-100'}`}>
                  <span className="font-medium">Telegram Bot:</span> {diagnosisResults.telegramBot.status} - {diagnosisResults.telegramBot.details}
                </div>
                {diagnosisResults.currentWebhook.status !== 'unknown' && (
                  <div className="p-2 rounded bg-gray-100">
                    <span className="font-medium">Current Webhook:</span> {diagnosisResults.currentWebhook.status} - {diagnosisResults.currentWebhook.details}
                  </div>
                )}
                {diagnosisResults.recommendations && diagnosisResults.recommendations.length > 0 && (
                  <div className="mt-3 p-3 bg-yellow-50 rounded border border-yellow-200">
                    <h5 className="font-medium text-yellow-900 mb-2">Recommendations:</h5>
                    <ul className="text-sm space-y-1">
                      {diagnosisResults.recommendations.map((rec: string, idx: number) => (
                        <li key={idx} className="text-yellow-800">• {rec}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Last Webhook Error Debug Info */}
          {lastWebhookError && (
            <div className="mt-4 p-4 bg-red-50 rounded-lg border border-red-200">
              <h4 className="font-medium text-red-900 mb-3">❌ Last Webhook Setup Error Debug</h4>
              <div className="text-sm space-y-2">
                {lastWebhookError.timestamp && (
                  <p><span className="font-medium">Timestamp:</span> {lastWebhookError.timestamp}</p>
                )}
                {lastWebhookError.step && (
                  <p><span className="font-medium">Failed at step:</span> {lastWebhookError.step}</p>
                )}
                {lastWebhookError.webhookUrl && (
                  <p><span className="font-medium">Webhook URL:</span> {lastWebhookError.webhookUrl}</p>
                )}
                {lastWebhookError.userId && (
                  <p><span className="font-medium">User ID:</span> {lastWebhookError.userId}</p>
                )}
                {lastWebhookError.secret && (
                  <p><span className="font-medium">Secret:</span> {lastWebhookError.secret}</p>
                )}
                {lastWebhookError.telegramResponse && (
                  <div className="mt-2">
                    <p className="font-medium">Telegram API Response:</p>
                    <pre className="text-xs bg-red-100 p-2 rounded mt-1 overflow-auto">
                      {JSON.stringify(lastWebhookError.telegramResponse, null, 2)}
                    </pre>
                  </div>
                )}
                {lastWebhookError.error && (
                  <p className="text-red-800"><span className="font-medium">Error:</span> {lastWebhookError.error}</p>
                )}
                <div className="mt-3 p-2 bg-red-100 rounded">
                  <p className="text-xs text-red-700">
                    💡 <strong>Tip:</strong> Check the browser console for more detailed logs. Use the "🔍 Diagnose" button to run comprehensive checks.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">API Keys</h3>
            <button
              type="button"
              onClick={handleValidateConfiguration}
              disabled={validating}
              className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
            >
              {validating ? "Validating..." : "Validate All"}
            </button>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                OpenRouter API Key
              </label>
              <input
                type="password"
                value={formData.OPENROUTER_API_KEY}
                onChange={(e) => handleInputChange("OPENROUTER_API_KEY", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="sk-or-..."
              />
              <p className="text-sm text-gray-500 mt-1">
                Required for AI processing. Get your key from{" "}
                <a href="https://openrouter.ai" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                  OpenRouter
                </a>. Stored securely in your private account.
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Linear API Key
              </label>
              <input
                type="password"
                value={formData.LINEAR_API_KEY}
                onChange={(e) => handleInputChange("LINEAR_API_KEY", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="lin_api_..."
              />
              <p className="text-sm text-gray-500 mt-1">
                Required for creating tasks in Linear. Generate from your{" "}
                <a href="https://linear.app/settings/api" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                  Linear settings
                </a>. Stored securely in your private account.
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Linear Team ID
              </label>
              <input
                type="text"
                value={formData.LINEAR_TEAM_ID}
                onChange={(e) => handleInputChange("LINEAR_TEAM_ID", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="team-id-here"
              />
              <p className="text-sm text-gray-500 mt-1">
                The ID of the Linear team where tasks should be created
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Telegram Bot Token
              </label>
              <input
                type="password"
                value={formData.TELEGRAM_BOT_TOKEN}
                onChange={(e) => handleInputChange("TELEGRAM_BOT_TOKEN", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="123456:ABC-DEF..."
              />
              <p className="text-sm text-gray-500 mt-1">
                Get this from{" "}
                <a href="https://t.me/BotFather" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                  @BotFather
                </a>{" "}
                on Telegram. Stored securely in your private account.
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Setup Instructions</h3>
          
          <div className="space-y-4 text-sm text-gray-600">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold mt-0.5">1</div>
              <div>
                <h4 className="font-medium text-gray-900">Create a Telegram Bot</h4>
                <p>Message @BotFather on Telegram and create a new bot. Copy the bot token above.</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold mt-0.5">2</div>
              <div>
                <h4 className="font-medium text-gray-900">Configure Webhook</h4>
                <p>Use the "Test Webhook" button above to automatically configure your bot's webhook.</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold mt-0.5">3</div>
              <div>
                <h4 className="font-medium text-gray-900">Get Linear Team ID</h4>
                <p>In Linear, go to your team settings and copy the team ID from the URL or API section.</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold mt-0.5">4</div>
              <div>
                <h4 className="font-medium text-gray-900">Test the Integration</h4>
                <p>Send a message to your bot with a task description, and it should appear in the dashboard.</p>
              </div>
            </div>
          </div>
        </div>

        <button
          type="submit"
          disabled={loading}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
        >
          {loading ? "Saving..." : "Save Settings 🔐"}
        </button>
      </form>
    </div>
  );
}
