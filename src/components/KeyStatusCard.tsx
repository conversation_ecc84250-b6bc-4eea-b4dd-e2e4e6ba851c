import { useState } from "react";

interface KeyStatus {
  exists: boolean;
  valid: boolean;
  encrypted: boolean;
  message: string;
  details?: string;
  teamName?: string;
  botUsername?: string;
}

interface KeyStatusCardProps {
  keyName: string;
  displayName: string;
  status: KeyStatus;
  onRevalidate?: () => void;
  isValidating?: boolean;
}

export function KeyStatusCard({ 
  keyName, 
  displayName, 
  status, 
  onRevalidate, 
  isValidating = false 
}: KeyStatusCardProps) {
  const [showDetails, setShowDetails] = useState(false);

  const getStatusColor = () => {
    if (!status.exists) return "gray";
    if (status.valid) return "green";
    if (status.exists && !status.valid) return "red";
    return "yellow";
  };

  const getStatusIcon = () => {
    const color = getStatusColor();
    const baseClasses = "w-3 h-3 rounded-full";
    
    switch (color) {
      case "green":
        return <div className={`${baseClasses} bg-green-500 shadow-green-200 shadow-lg animate-pulse`} />;
      case "red":
        return <div className={`${baseClasses} bg-red-500 shadow-red-200 shadow-lg`} />;
      case "yellow":
        return <div className={`${baseClasses} bg-yellow-500 shadow-yellow-200 shadow-lg animate-pulse`} />;
      default:
        return <div className={`${baseClasses} bg-gray-400`} />;
    }
  };

  const getEncryptionIcon = () => {
    if (status.encrypted) {
      return (
        <div className="flex items-center gap-1 text-green-700 bg-green-100 px-2 py-1 rounded-full text-xs font-medium">
          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          Encrypted
        </div>
      );
    }
    return (
      <div className="flex items-center gap-1 text-gray-600 bg-gray-100 px-2 py-1 rounded-full text-xs font-medium">
        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
        </svg>
        Plain Text
      </div>
    );
  };

  const getBorderColor = () => {
    const color = getStatusColor();
    switch (color) {
      case "green":
        return "border-green-200 bg-green-50";
      case "red":
        return "border-red-200 bg-red-50";
      case "yellow":
        return "border-yellow-200 bg-yellow-50";
      default:
        return "border-gray-200 bg-gray-50";
    }
  };

  const getHeaderTextColor = () => {
    const color = getStatusColor();
    switch (color) {
      case "green":
        return "text-green-900";
      case "red":
        return "text-red-900";
      case "yellow":
        return "text-yellow-900";
      default:
        return "text-gray-900";
    }
  };

  return (
    <div className={`rounded-lg border-2 ${getBorderColor()} p-4 transition-all duration-200 hover:shadow-md`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-3">
          {getStatusIcon()}
          <div>
            <h4 className={`font-semibold ${getHeaderTextColor()}`}>
              {displayName}
            </h4>
            <p className="text-sm text-gray-600 font-mono">{keyName}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {getEncryptionIcon()}
          {onRevalidate && (
            <button
              onClick={onRevalidate}
              disabled={isValidating}
              className="p-1 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded transition-colors"
              title="Revalidate this key"
            >
              <svg 
                className={`w-4 h-4 ${isValidating ? 'animate-spin' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Status Message */}
      <div className="mb-3">
        <p className={`text-sm font-medium ${getHeaderTextColor()}`}>
          {status.message}
        </p>
        {status.details && (
          <p className="text-sm text-gray-600 mt-1">
            {status.details}
          </p>
        )}
      </div>

      {/* Additional Info */}
      {(status.teamName || status.botUsername) && (
        <div className="mb-3 p-2 bg-blue-50 border border-blue-200 rounded text-sm">
          {status.teamName && (
            <p><span className="font-medium text-blue-900">Team:</span> {status.teamName}</p>
          )}
          {status.botUsername && (
            <p><span className="font-medium text-blue-900">Bot:</span> @{status.botUsername}</p>
          )}
        </div>
      )}

      {/* Expandable Details */}
      {(status.details || status.exists) && (
        <div className="border-t border-gray-200 pt-3">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
          >
            <svg 
              className={`w-4 h-4 transition-transform ${showDetails ? 'rotate-180' : ''}`} 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
            {showDetails ? 'Hide' : 'Show'} Details
          </button>
          
          {showDetails && (
            <div className="mt-3 p-3 bg-gray-100 rounded text-xs font-mono space-y-2">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-gray-600">Exists:</span>
                  <span className={`ml-2 font-medium ${status.exists ? 'text-green-700' : 'text-red-700'}`}>
                    {status.exists ? '✓ Yes' : '✗ No'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Valid:</span>
                  <span className={`ml-2 font-medium ${status.valid ? 'text-green-700' : 'text-red-700'}`}>
                    {status.valid ? '✓ Yes' : '✗ No'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-600">Encrypted:</span>
                  <span className={`ml-2 font-medium ${status.encrypted ? 'text-green-700' : 'text-orange-700'}`}>
                    {status.encrypted ? '🔒 Yes' : '🔓 No'}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}