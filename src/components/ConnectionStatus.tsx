import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { motion } from "framer-motion";
import { 
  CheckCircle2, 
  AlertCircle, 
  XCircle, 
  Clock,
  Wifi,
  WifiOff,
  RefreshCw
} from "lucide-react";

export function ConnectionStatus() {
  const settings = useQuery(api.settings.getAllSettings);
  const webhookStats = useQuery(api.webhooks.getWebhookStats);

  const getConnectionStatus = () => {
    if (!settings) return { status: "loading", message: "Loading..." };

    const settingsMap = settings.reduce((acc, setting) => {
      acc[setting.key] = setting.value;
      return acc;
    }, {} as Record<string, string>);

    const hasBot = !!settingsMap.TELEGRAM_BOT_TOKEN;
    const hasLinear = !!settingsMap.LINEAR_API_KEY && !!settingsMap.LINEAR_TEAM_ID;
    const hasAI = !!settingsMap.OPENROUTER_API_KEY;

    if (!hasBot && !hasLinear && !hasAI) {
      return { 
        status: "disconnected", 
        message: "Setup required",
        details: "Complete the onboarding to get started"
      };
    }

    if (hasBot && hasLinear && hasAI) {
      const recentActivity = webhookStats?.last24h || 0;
      if (recentActivity > 0) {
        return { 
          status: "connected", 
          message: "Active",
          details: `${recentActivity} requests in last 24h`
        };
      } else {
        return { 
          status: "configured", 
          message: "Ready",
          details: "Waiting for messages"
        };
      }
    }

    return { 
      status: "partial", 
      message: "Incomplete setup",
      details: "Some services not configured"
    };
  };

  const { status, message, details } = getConnectionStatus();

  const getStatusConfig = () => {
    switch (status) {
      case "connected":
        return {
          icon: CheckCircle2,
          color: "text-green-600",
          bg: "bg-green-100",
          border: "border-green-200",
          pulse: "animate-pulse",
          indicator: "bg-green-500"
        };
      case "configured":
        return {
          icon: Clock,
          color: "text-blue-600",
          bg: "bg-blue-100",
          border: "border-blue-200",
          pulse: "",
          indicator: "bg-blue-500"
        };
      case "partial":
        return {
          icon: AlertCircle,
          color: "text-yellow-600",
          bg: "bg-yellow-100",
          border: "border-yellow-200",
          pulse: "",
          indicator: "bg-yellow-500"
        };
      case "disconnected":
        return {
          icon: XCircle,
          color: "text-red-600",
          bg: "bg-red-100",
          border: "border-red-200",
          pulse: "",
          indicator: "bg-red-500"
        };
      default:
        return {
          icon: RefreshCw,
          color: "text-gray-600",
          bg: "bg-gray-100",
          border: "border-gray-200",
          pulse: "animate-spin",
          indicator: "bg-gray-500"
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`relative p-4 rounded-xl border-2 ${config.bg} ${config.border} transition-all duration-200 hover:shadow-md`}
    >
      {/* Status Indicator Dot */}
      <div className="absolute top-3 right-3">
        <div className="relative">
          <div className={`w-3 h-3 rounded-full ${config.indicator}`} />
          {status === "connected" && (
            <div className="absolute inset-0 w-3 h-3 rounded-full bg-green-500 animate-ping opacity-75" />
          )}
        </div>
      </div>

      <div className="flex items-start gap-3">
        <motion.div
          animate={status === "loading" ? { rotate: 360 } : {}}
          transition={status === "loading" ? { duration: 1, repeat: Infinity, ease: "linear" } : {}}
          className={`p-2 rounded-lg ${config.bg} ${config.color}`}
        >
          <Icon className="w-5 h-5" />
        </motion.div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            {status === "connected" ? (
              <Wifi className="w-4 h-4 text-green-600" />
            ) : (
              <WifiOff className="w-4 h-4 text-gray-400" />
            )}
            <h3 className={`font-semibold ${config.color}`}>
              {message}
            </h3>
          </div>
          <p className="text-sm text-gray-600">
            {details}
          </p>
          {status === "partial" && (
            <div className="mt-2 text-xs text-gray-500">
              Check Settings → Setup to complete configuration
            </div>
          )}
        </div>
      </div>

      {/* Quick Stats */}
      {webhookStats && status !== "disconnected" && (
        <div className="mt-3 pt-3 border-t border-gray-200/50">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Success Rate</span>
            <span className="font-medium">
              {webhookStats.total > 0 
                ? Math.round((webhookStats.success / webhookStats.total) * 100) 
                : 0}%
            </span>
          </div>
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Total Requests</span>
            <span className="font-medium">{webhookStats.total}</span>
          </div>
        </div>
      )}
    </motion.div>
  );
}