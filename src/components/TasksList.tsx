import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

export function TasksList() {
  const tasks = useQuery(api.tasks.getAllTasks);

  if (!tasks) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="border rounded-lg p-4 animate-pulse">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              </div>
              <div className="flex flex-col gap-2">
                <div className="h-6 bg-gray-200 rounded w-16"></div>
                <div className="h-6 bg-gray-200 rounded w-16"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (tasks.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="text-gray-300 text-8xl mb-6">📝</div>
        <h3 className="text-xl font-semibold text-gray-900 mb-3">No tasks yet</h3>
        <p className="text-gray-500 max-w-md mx-auto">
          Send a message to your Telegram bot to create your first task. Make sure your webhook is configured properly.
        </p>
        <div className="mt-6">
          <a
            href="#settings"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Configure Settings →
          </a>
        </div>
      </div>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "synced": return "✅";
      case "partial_sync": return "⚠️";
      case "syncing": return "🔄";
      case "failed": return "❌";
      default: return "⏳";
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "urgent": return "🔴";
      case "high": return "🟠";
      case "medium": return "🟡";
      case "low": return "🟢";
      default: return "⚪";
    }
  };

  return (
    <div className="space-y-4">
      {tasks.map((task) => (
        <div key={task._id} className="border rounded-lg p-6 hover:shadow-md transition-all duration-200 bg-white">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-lg">{getPriorityIcon(task.priority)}</span>
                <h3 className="font-semibold text-gray-900 text-lg">{task.title}</h3>
              </div>
              <p className="text-gray-600 mb-3 leading-relaxed">{task.description}</p>
              
              {task.subtasks.length > 0 && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
                    <span>📋</span>
                    Subtasks ({task.subtasks.length})
                  </h4>
                  <div className="space-y-2">
                    {task.subtasks.map((subtask, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-md">
                        <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-800">{subtask.title}</p>
                          {subtask.description && (
                            <p className="text-sm text-gray-600 mt-1">{subtask.description}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            <div className="flex flex-col items-end gap-3 ml-6">
              <div className="flex items-center gap-2">
                <span className="text-lg">{getStatusIcon(task.status)}</span>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                  task.status === "synced" ? "bg-green-100 text-green-800" :
                  task.status === "partial_sync" ? "bg-yellow-100 text-yellow-800" :
                  task.status === "syncing" ? "bg-blue-100 text-blue-800" :
                  task.status === "failed" ? "bg-red-100 text-red-800" :
                  "bg-gray-100 text-gray-800"
                }`}>
                  {task.status === "partial_sync" ? "partial sync" : task.status}
                </span>
              </div>
              
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                task.priority === "urgent" ? "bg-red-100 text-red-800" :
                task.priority === "high" ? "bg-orange-100 text-orange-800" :
                task.priority === "medium" ? "bg-yellow-100 text-yellow-800" :
                "bg-green-100 text-green-800"
              }`}>
                {task.priority} priority
              </span>
            </div>
          </div>
          
          {task.linearUrl && (
            <div className="mt-4 pt-4 border-t border-gray-100">
              <a
                href={task.linearUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors"
              >
                <span>🔗</span>
                View in Linear
                <span>→</span>
              </a>
            </div>
          )}
          
          {task.errorMessage && (
            <div className="mt-4 pt-4 border-t border-gray-100">
              <div className="flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
                <span className="text-red-500 text-sm">⚠️</span>
                <div>
                  <p className="text-red-800 text-sm font-medium">Error occurred</p>
                  <p className="text-red-700 text-sm mt-1">{task.errorMessage}</p>
                </div>
              </div>
            </div>
          )}
          
          <div className="mt-4 pt-4 border-t border-gray-100 flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center gap-2">
              <span>💬</span>
              <span>From: "{task.telegramMessage?.text?.substring(0, 80)}..."</span>
            </div>
            <span>{new Date(task._creationTime).toLocaleDateString()}</span>
          </div>
        </div>
      ))}
    </div>
  );
}
