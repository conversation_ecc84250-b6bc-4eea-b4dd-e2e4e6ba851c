import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState } from "react";

export function WebhookLogs() {
  const logs = useQuery(api.webhooks.getRecentWebhookLogs);
  const [expandedLogs, setExpandedLogs] = useState<Set<string>>(new Set());

  const toggleExpanded = (logId: string) => {
    const newExpanded = new Set(expandedLogs);
    if (newExpanded.has(logId)) {
      newExpanded.delete(logId);
    } else {
      newExpanded.add(logId);
    }
    setExpandedLogs(newExpanded);
  };

  if (!logs) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="border rounded-lg p-4 animate-pulse">
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <div className="h-6 bg-gray-200 rounded w-16"></div>
                  <div className="h-4 bg-gray-200 rounded w-32"></div>
                </div>
                <div className="h-3 bg-gray-200 rounded w-48"></div>
              </div>
              <div className="h-6 bg-gray-200 rounded w-20"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (logs.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="text-gray-300 text-8xl mb-6">🔗</div>
        <h3 className="text-xl font-semibold text-gray-900 mb-3">No webhook logs yet</h3>
        <p className="text-gray-500 max-w-md mx-auto">
          Webhook requests will appear here once your Telegram bot starts sending messages to the configured endpoint.
        </p>
        <div className="mt-6">
          <a
            href="#settings"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Configure Webhook →
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {logs.map((log) => {
        const isExpanded = expandedLogs.has(log._id);
        let parsedBody;
        try {
          parsedBody = JSON.parse(log.body);
        } catch {
          parsedBody = log.body;
        }

        return (
          <div key={log._id} className="border rounded-lg p-4 hover:shadow-md transition-shadow bg-white">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <span className="text-lg">{log.status === "success" ? "✅" : "❌"}</span>
                  <span className="font-mono text-sm bg-gray-100 px-3 py-1 rounded-md font-medium">
                    {log.method}
                  </span>
                  <span className="text-sm text-gray-600 font-medium">{log.path}</span>
                </div>
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <span className="flex items-center gap-1">
                    <span>🕒</span>
                    {new Date(log.timestamp).toLocaleString()}
                  </span>
                  {parsedBody?.message && (
                    <span className="flex items-center gap-1">
                      <span>💬</span>
                      Message ID: {parsedBody.message.message_id}
                    </span>
                  )}
                </div>
              </div>
              
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                log.status === "success" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
              }`}>
                {log.status}
              </span>
            </div>
            
            {log.errorMessage && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <div className="flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
                  <span className="text-red-500 text-sm">⚠️</span>
                  <div>
                    <p className="text-red-800 text-sm font-medium">Webhook Error</p>
                    <p className="text-red-700 text-sm mt-1">{log.errorMessage}</p>
                  </div>
                </div>
              </div>
            )}

            {parsedBody?.message && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                  <div className="flex items-start gap-2">
                    <span className="text-blue-600 text-sm">💬</span>
                    <div className="flex-1">
                      <p className="text-blue-800 text-sm font-medium">Message Content</p>
                      <p className="text-blue-700 text-sm mt-1">"{parsedBody.message.text}"</p>
                      <p className="text-blue-600 text-xs mt-1">
                        From: {parsedBody.message.from?.first_name} (@{parsedBody.message.from?.username})
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            <div className="mt-3 pt-3 border-t border-gray-100">
              <button
                onClick={() => toggleExpanded(log._id)}
                className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
              >
                <span className={`transform transition-transform ${isExpanded ? "rotate-90" : ""}`}>
                  ▶️
                </span>
                {isExpanded ? "Hide" : "Show"} raw request body
              </button>
              
              {isExpanded && (
                <div className="mt-3">
                  <pre className="p-4 bg-gray-50 rounded-md text-xs overflow-x-auto border">
                    {typeof parsedBody === "string" ? parsedBody : JSON.stringify(parsedBody, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}
