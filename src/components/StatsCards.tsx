import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

export function StatsCards() {
  const messageStats = useQuery(api.telegram.getMessageStats);
  const taskStats = useQuery(api.tasks.getTaskStats);
  const webhookStats = useQuery(api.webhooks.getWebhookStats);

  if (!messageStats || !taskStats || !webhookStats) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-2/3"></div>
          </div>
        ))}
      </div>
    );
  }

  const cards = [
    {
      title: "Total Messages",
      value: messageStats.total,
      subtitle: `${messageStats.pending} pending`,
      color: "blue",
      icon: "💬",
      trend: messageStats.total > 0 ? "+12%" : "0%",
    },
    {
      title: "Tasks Created",
      value: taskStats.total,
      subtitle: `${taskStats.synced} synced to Linear`,
      color: "green",
      icon: "✅",
      trend: taskStats.total > 0 ? "+8%" : "0%",
    },
    {
      title: "Webhook Requests",
      value: webhookStats.total,
      subtitle: `${webhookStats.last24h} in last 24h`,
      color: "purple",
      icon: "🔗",
      trend: webhookStats.last24h > 0 ? "Active" : "Inactive",
    },
    {
      title: "Success Rate",
      value: webhookStats.total > 0 ? Math.round((webhookStats.success / webhookStats.total) * 100) + "%" : "0%",
      subtitle: `${webhookStats.error} errors`,
      color: webhookStats.error === 0 ? "green" : "red",
      icon: webhookStats.error === 0 ? "🎯" : "⚠️",
      trend: webhookStats.error === 0 ? "Excellent" : "Needs attention",
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {cards.map((card, index) => (
        <div key={index} className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6 border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <div className="text-2xl">{card.icon}</div>
            <div className={`px-2 py-1 rounded-full text-xs font-medium ${
              card.color === "blue" ? "bg-blue-100 text-blue-800" :
              card.color === "green" ? "bg-green-100 text-green-800" :
              card.color === "purple" ? "bg-purple-100 text-purple-800" :
              card.color === "red" ? "bg-red-100 text-red-800" : "bg-gray-100 text-gray-800"
            }`}>
              {card.trend}
            </div>
          </div>
          
          <div>
            <p className="text-sm font-medium text-gray-600 mb-1">{card.title}</p>
            <p className="text-3xl font-bold text-gray-900 mb-1">{card.value}</p>
            <p className="text-sm text-gray-500">{card.subtitle}</p>
          </div>
        </div>
      ))}
    </div>
  );
}
