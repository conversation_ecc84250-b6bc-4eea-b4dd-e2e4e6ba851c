import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";

export function MessagesList() {
  const messages = useQuery(api.telegram.getRecentMessages);

  if (!messages) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="border rounded-lg p-4 animate-pulse">
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div className="h-6 bg-gray-200 rounded w-20"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (messages.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="text-gray-300 text-8xl mb-6">💬</div>
        <h3 className="text-xl font-semibold text-gray-900 mb-3">No messages yet</h3>
        <p className="text-gray-500 max-w-md mx-auto">
          Configure your Telegram webhook to start receiving messages from your bot.
        </p>
        <div className="mt-6">
          <a
            href="#settings"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Configure Webhook →
          </a>
        </div>
      </div>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed": return "✅";
      case "processing": return "🔄";
      case "failed": return "❌";
      default: return "⏳";
    }
  };

  return (
    <div className="space-y-4">
      {messages.map((message) => (
        <div key={message._id} className="border rounded-lg p-4 hover:shadow-md transition-shadow bg-white">
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1">
              <div className="flex items-start gap-3 mb-3">
                <div className="text-2xl">💬</div>
                <div className="flex-1">
                  <p className="text-gray-900 leading-relaxed mb-2">{message.text}</p>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span className="flex items-center gap-1">
                      <span>🕒</span>
                      {new Date(message.date * 1000).toLocaleString()}
                    </span>
                    <span className="flex items-center gap-1">
                      <span>💬</span>
                      Chat ID: {message.chatId}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-2 ml-4">
              <span className="text-lg">{getStatusIcon(message.processingStatus)}</span>
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                message.processingStatus === "completed" ? "bg-green-100 text-green-800" :
                message.processingStatus === "processing" ? "bg-blue-100 text-blue-800" :
                message.processingStatus === "failed" ? "bg-red-100 text-red-800" :
                "bg-gray-100 text-gray-800"
              }`}>
                {message.processingStatus}
              </span>
            </div>
          </div>
          
          {message.errorMessage && (
            <div className="mt-3 pt-3 border-t border-gray-100">
              <div className="flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
                <span className="text-red-500 text-sm">⚠️</span>
                <div>
                  <p className="text-red-800 text-sm font-medium">Processing failed</p>
                  <p className="text-red-700 text-sm mt-1">{message.errorMessage}</p>
                </div>
              </div>
            </div>
          )}

          {message.processingStatus === "processing" && (
            <div className="mt-3 pt-3 border-t border-gray-100">
              <div className="flex items-center gap-2 text-blue-600 text-sm">
                <div className="animate-spin w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full"></div>
                <span>Processing with AI...</span>
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
