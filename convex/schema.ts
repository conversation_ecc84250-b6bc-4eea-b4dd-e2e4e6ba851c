import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

const applicationTables = {
  telegramMessages: defineTable({
    userId: v.id("users"), // Associate messages with users
    messageId: v.number(),
    text: v.string(),
    date: v.number(),
    chatId: v.number(),
    processed: v.boolean(),
    processingStatus: v.union(
      v.literal("pending"),
      v.literal("processing"),
      v.literal("completed"),
      v.literal("failed")
    ),
    errorMessage: v.optional(v.string()),
  }).index("by_user_and_processed", ["userId", "processed"])
    .index("by_user_and_status", ["userId", "processingStatus"])
    .index("by_user", ["userId"]),

  tasks: defineTable({
    userId: v.id("users"), // Associate tasks with users
    telegramMessageId: v.id("telegramMessages"),
    title: v.string(),
    description: v.string(),
    priority: v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("urgent")
    ),
    linearTaskId: v.optional(v.string()),
    linearUrl: v.optional(v.string()),
    status: v.union(
      v.literal("created"),
      v.literal("syncing"),
      v.literal("synced"),
      v.literal("failed")
    ),
    subtasks: v.array(v.object({
      title: v.string(),
      description: v.string(),
      linearTaskId: v.optional(v.string()),
      status: v.union(
        v.literal("created"),
        v.literal("syncing"),
        v.literal("synced"),
        v.literal("failed")
      )
    })),
    errorMessage: v.optional(v.string()),
  }).index("by_user_and_status", ["userId", "status"])
    .index("by_user_and_telegram_message", ["userId", "telegramMessageId"])
    .index("by_user", ["userId"]),

  webhookLogs: defineTable({
    userId: v.optional(v.id("users")), // Associate logs with users when possible
    timestamp: v.number(),
    method: v.string(),
    path: v.string(),
    body: v.string(),
    status: v.union(
      v.literal("success"),
      v.literal("error")
    ),
    errorMessage: v.optional(v.string()),
  }).index("by_timestamp", ["timestamp"])
    .index("by_status", ["status"])
    .index("by_user", ["userId"]),

  settings: defineTable({
    userId: v.id("users"), // Associate settings with users
    key: v.string(),
    value: v.string(),
    isEncrypted: v.boolean(), // Track if value is encrypted
  }).index("by_user_and_key", ["userId", "key"])
    .index("by_user", ["userId"]),

  securityLogs: defineTable({
    userId: v.optional(v.id("users")), // User associated with the event (if any)
    timestamp: v.number(),
    eventType: v.union(
      v.literal("auth_failure"),
      v.literal("rate_limit_exceeded"),
      v.literal("invalid_signature"),
      v.literal("suspicious_request"),
      v.literal("data_access"),
      v.literal("setting_change"),
      v.literal("encryption_error")
    ),
    details: v.string(),
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),
    requestUrl: v.optional(v.string()),
    severity: v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("critical")
    ),
  }).index("by_timestamp", ["timestamp"])
    .index("by_event_type", ["eventType"])
    .index("by_severity", ["severity"])
    .index("by_user", ["userId"]),
};

export default defineSchema({
  ...authTables,
  ...applicationTables,
});
