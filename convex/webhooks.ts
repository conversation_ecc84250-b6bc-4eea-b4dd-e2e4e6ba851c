import { mutation, query, action } from "./_generated/server";
import { v } from "convex/values";
import { internal, api } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";

export const logWebhookRequest = mutation({
  args: {
    method: v.string(),
    path: v.string(),
    body: v.string(),
    status: v.union(v.literal("success"), v.literal("error")),
    errorMessage: v.optional(v.string()),
    userId: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("webhookLogs", {
      userId: args.userId,
      timestamp: Date.now(),
      method: args.method,
      path: args.path,
      body: args.body,
      status: args.status,
      errorMessage: args.errorMessage,
    });
  },
});

export const getRecentWebhookLogs = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    return await ctx.db
      .query("webhookLogs")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .order("desc")
      .take(50);
  },
});

export const getWebhookStats = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return {
        total: 0,
        success: 0,
        error: 0,
        last24h: 0,
      };
    }

    const logs = await ctx.db
      .query("webhookLogs")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();
    
    const stats = {
      total: logs.length,
      success: logs.filter(l => l.status === "success").length,
      error: logs.filter(l => l.status === "error").length,
      last24h: logs.filter(l => l.timestamp > Date.now() - 24 * 60 * 60 * 1000).length,
    };

    return stats;
  },
});

export const testWebhook = action({
  args: {
    botToken: v.string(),
    webhookUrl: v.string(),
  },
  handler: async (ctx, args) => {
    const debugInfo = {
      timestamp: new Date().toISOString(),
      step: "",
      webhookUrl: args.webhookUrl,
      botToken: args.botToken.substring(0, 10) + "...", // Only show first 10 chars for security
      userId: null as string | null,
      secret: null as string | null,
      telegramResponse: null as any,
      error: null as string | null,
    };

    try {
      debugInfo.step = "1. Checking authentication";
      console.log("🔐 Debug - Step 1: Checking authentication");
      
      const userId = await getAuthUserId(ctx);
      if (!userId) {
        debugInfo.error = "Not authenticated";
        console.log("❌ Debug - Authentication failed");
        throw new Error("Not authenticated");
      }
      
      debugInfo.userId = userId;
      console.log("✅ Debug - Authentication successful, userId:", userId);

      debugInfo.step = "2. Retrieving webhook secret";
      console.log("🔑 Debug - Step 2: Retrieving webhook secret");
      
      let secret = await ctx.runQuery(internal.settings.getSetting, {
        key: "TELEGRAM_WEBHOOK_SECRET",
        userId,
      });

      if (!secret) {
        debugInfo.error = "Webhook secret not configured";
        console.log("❌ Debug - No webhook secret found");
        throw new Error("Webhook secret not configured. Generate and save it first.");
      }

      // Sanitize/validate the secret to avoid Telegram "unallowed characters"
      const originalSecret = secret;
      secret = secret.trim();
      const allowedRe = /^[A-Za-z0-9_-]+$/;
      if (!allowedRe.test(secret)) {
        const cleaned = secret.replace(/[^A-Za-z0-9_-]/g, "");
        console.log("⚠️  Debug - Secret contained disallowed characters. Cleaned length:", cleaned.length);
        if (cleaned.length < 32) {
          debugInfo.error = "Webhook secret contains disallowed characters or is too short after cleaning";
          throw new Error("Webhook secret invalid: only A–Z, a–z, 0–9, _ and - allowed");
        }
        secret = cleaned;
      }

      debugInfo.secret = secret.substring(0, 8) + "..."; // Only show first 8 chars
      console.log("✅ Debug - Webhook secret found:", debugInfo.secret);

      debugInfo.step = "3. Testing webhook URL accessibility";
      console.log("🌐 Debug - Step 3: Testing webhook URL:", args.webhookUrl);
      
      try {
        const urlTest = await fetch(args.webhookUrl, { method: "GET" });
        console.log("🌐 Debug - Webhook URL test status:", urlTest.status);
        if (urlTest.status === 404) {
          console.log("⚠️  Debug - WARNING: Webhook URL returns 404, but continuing...");
        }
      } catch (urlError) {
        console.log("⚠️  Debug - Webhook URL test failed:", urlError);
      }

      debugInfo.step = "4. Calling Telegram setWebhook API";
      console.log("📡 Debug - Step 4: Calling Telegram setWebhook API");
      
      const telegramPayload = {
        url: args.webhookUrl,
        secret_token: secret,
        allowed_updates: ["message", "edited_message"],
        drop_pending_updates: false,
      };
      
      console.log("📡 Debug - Telegram payload:", {
        ...telegramPayload,
        secret_token: telegramPayload.secret_token.substring(0, 8) + "..."
      });

      const response = await fetch(`https://api.telegram.org/bot${args.botToken}/setWebhook`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(telegramPayload),
      });

      const data = await response.json();
      debugInfo.telegramResponse = data;
      
      console.log("📡 Debug - Telegram API response status:", response.status);
      console.log("📡 Debug - Telegram API response data:", data);
      
      if (!response.ok || !data.ok) {
        debugInfo.error = `Telegram API error: ${data.description || 'Unknown error'}`;
        console.log("❌ Debug - Telegram API error:", debugInfo.error);
        throw new Error(`Telegram API Error (${response.status}): ${data.description || "Failed to set webhook"}`);
      }

      console.log("✅ Debug - Webhook configured successfully!");
      
      return { 
        success: true, 
        message: "Webhook configured successfully",
        debug: debugInfo
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      debugInfo.error = errorMessage;
      
      console.log("❌ Debug - Final error at step:", debugInfo.step);
      console.log("❌ Debug - Error:", errorMessage);
      console.log("❌ Debug - Full debug info:", debugInfo);
      
      return { 
        success: false, 
        message: errorMessage,
        debug: debugInfo
      };
    }
  },
});

export const getWebhookInfo = action({
  args: {
    botToken: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const response = await fetch(`https://api.telegram.org/bot${args.botToken}/getWebhookInfo`);
      const data = await response.json();
      
      if (!response.ok || !data.ok) {
        throw new Error(data.description || "Failed to get webhook info");
      }

      return { success: true, data: data.result };
    } catch (error) {
      return { 
        success: false, 
        message: error instanceof Error ? error.message : "Unknown error" 
      };
    }
  },
});

export const diagnoseWebhookSetup = action({
  args: {
    botToken: v.string(),
  },
  handler: async (ctx, args) => {
    const diagnosis = {
      timestamp: new Date().toISOString(),
      authentication: { status: "unknown", details: "" },
      webhookSecret: { status: "unknown", details: "" },
      webhookUrl: { status: "unknown", url: "", details: "" },
      httpRoutes: { status: "unknown", details: "" },
      telegramBot: { status: "unknown", details: "" },
      currentWebhook: { status: "unknown", details: "", data: null as any },
      recommendations: [] as string[],
    };

    // 1. Check Authentication
    try {
      const userId = await getAuthUserId(ctx);
      if (userId) {
        diagnosis.authentication = { status: "✅ pass", details: `Authenticated as user: ${userId}` };
      } else {
        diagnosis.authentication = { status: "❌ fail", details: "Not authenticated" };
        diagnosis.recommendations.push("Please sign in to your account");
      }
    } catch (error) {
      diagnosis.authentication = { status: "❌ fail", details: `Auth error: ${error}` };
    }

    // 2. Check Webhook Secret
    if (diagnosis.authentication.status === "✅ pass") {
      try {
        const userId = await getAuthUserId(ctx);
        const secret = await ctx.runQuery(internal.settings.getSetting, {
          key: "TELEGRAM_WEBHOOK_SECRET",
          userId: userId!,
        });
        
        if (secret && secret.length >= 32) {
          diagnosis.webhookSecret = { 
            status: "✅ pass", 
            details: `Secret configured (${secret.length} chars, starts with ${secret.substring(0, 8)}...)` 
          };
        } else if (secret) {
          diagnosis.webhookSecret = { 
            status: "⚠️  warning", 
            details: `Secret too short (${secret.length} chars). Should be at least 32 characters.` 
          };
          diagnosis.recommendations.push("Generate a new webhook secret (32+ characters)");
        } else {
          diagnosis.webhookSecret = { status: "❌ fail", details: "No webhook secret configured" };
          diagnosis.recommendations.push("Generate and save a webhook secret");
        }
      } catch (error) {
        diagnosis.webhookSecret = { status: "❌ fail", details: `Error retrieving secret: ${error}` };
      }
    }

    // 3. Check Webhook URL
    try {
      const baseUrl = await ctx.runQuery(api.webhooks.getConvexSiteUrl, {});
      const webhookUrl = `${baseUrl}/telegram/webhook`;
      diagnosis.webhookUrl.url = webhookUrl;
      
      console.log("🌐 Diagnosis - Testing webhook URL:", webhookUrl);
      
      try {
        const response = await fetch(webhookUrl, { method: "GET" });
        if (response.status === 200) {
          diagnosis.webhookUrl = { 
            status: "✅ pass", 
            url: webhookUrl,
            details: `Webhook endpoint accessible (HTTP ${response.status})` 
          };
        } else if (response.status === 404) {
          diagnosis.webhookUrl = { 
            status: "❌ fail", 
            url: webhookUrl,
            details: `Webhook endpoint not found (HTTP 404). HTTP routes may not be deployed.` 
          };
          diagnosis.recommendations.push("Deploy HTTP routes with: npx convex deploy");
        } else {
          diagnosis.webhookUrl = { 
            status: "⚠️  warning", 
            url: webhookUrl,
            details: `Unexpected response (HTTP ${response.status})` 
          };
        }
      } catch (urlError) {
        diagnosis.webhookUrl = { 
          status: "❌ fail", 
          url: webhookUrl,
          details: `Network error: ${urlError}` 
        };
        diagnosis.recommendations.push("Check internet connection and Convex deployment status");
      }
    } catch (error) {
      diagnosis.webhookUrl = { 
        status: "❌ fail", 
        url: "",
        details: `Error building URL: ${error}` 
      };
    }

    // 4. Check Telegram Bot
    try {
      console.log("🤖 Diagnosis - Testing Telegram bot token");
      const response = await fetch(`https://api.telegram.org/bot${args.botToken}/getMe`);
      const data = await response.json();
      
      if (data.ok) {
        diagnosis.telegramBot = { 
          status: "✅ pass", 
          details: `Bot valid: @${data.result.username} (${data.result.first_name})` 
        };
      } else {
        diagnosis.telegramBot = { 
          status: "❌ fail", 
          details: `Invalid bot token: ${data.description}` 
        };
        diagnosis.recommendations.push("Verify your Telegram bot token is correct");
      }
    } catch (error) {
      diagnosis.telegramBot = { status: "❌ fail", details: `Bot test failed: ${error}` };
    }

    // 5. Check Current Webhook Status
    if (diagnosis.telegramBot.status === "✅ pass") {
      try {
        console.log("📡 Diagnosis - Getting current webhook info");
        const response = await fetch(`https://api.telegram.org/bot${args.botToken}/getWebhookInfo`);
        const data = await response.json();
        
        if (data.ok) {
          const webhookInfo = data.result;
          diagnosis.currentWebhook.data = webhookInfo;
          
          if (webhookInfo.url) {
            diagnosis.currentWebhook = {
              status: "ℹ️  info",
              details: `Currently set to: ${webhookInfo.url}. Pending: ${webhookInfo.pending_update_count}`,
              data: webhookInfo
            };
            
            if (webhookInfo.last_error_message) {
              diagnosis.currentWebhook.details += `. Last error: ${webhookInfo.last_error_message}`;
              diagnosis.recommendations.push(`Fix webhook error: ${webhookInfo.last_error_message}`);
            }
          } else {
            diagnosis.currentWebhook = {
              status: "ℹ️  info",
              details: "No webhook currently configured",
              data: webhookInfo
            };
          }
        } else {
          diagnosis.currentWebhook = { 
            status: "❌ fail", 
            details: `Failed to get webhook info: ${data.description}`,
            data: null 
          };
        }
      } catch (error) {
        diagnosis.currentWebhook = { 
          status: "❌ fail", 
          details: `Error getting webhook info: ${error}`,
          data: null 
        };
      }
    }

    // Generate overall status
    const failCount = Object.values(diagnosis).filter(item => 
      typeof item === 'object' && item !== null && 'status' in item && item.status.includes('❌')
    ).length;
    
    const warningCount = Object.values(diagnosis).filter(item => 
      typeof item === 'object' && item !== null && 'status' in item && item.status.includes('⚠️')
    ).length;

    if (failCount === 0 && warningCount === 0) {
      diagnosis.recommendations.push("✅ All checks passed! You should be able to set up the webhook.");
    } else if (failCount > 0) {
      diagnosis.recommendations.unshift(`❌ ${failCount} critical issues found. Fix these first.`);
    } else {
      diagnosis.recommendations.unshift(`⚠️  ${warningCount} warnings found. Consider addressing these.`);
    }

    console.log("🔍 Diagnosis complete:", diagnosis);
    return diagnosis;
  },
});

export const getConvexSiteUrl = query({
  args: {},
  handler: async (ctx) => {
    // Prefer explicit site URL if set
    const envSite = process.env.CONVEX_SITE_URL;
    if (envSite) return envSite;

    // Derive from CONVEX_CLOUD_URL by swapping domain
    const cloudUrl = process.env.CONVEX_CLOUD_URL; // e.g., https://fastidious-foo-123.convex.cloud
    if (cloudUrl) {
      try {
        const u = new URL(cloudUrl);
        return `${u.protocol}//${u.hostname.replace('.convex.cloud', '.convex.site')}`;
      } catch {}
    }

    // Fallback to dev deployment
    return "https://fastidious-mosquito-539.convex.site";
  },
});

export const buildWebhookUrl = query({
  args: {
    secret: v.optional(v.string()), // Accept secret but don't use it in URL
  },
  returns: v.string(),
  handler: async (ctx, args): Promise<string> => {
    // Always point to Convex HTTP route defined in convex/http.ts
    const convexUrl: string = await ctx.runQuery(api.webhooks.getConvexSiteUrl, {});

    // Return base URL; secret is conveyed in header by Telegram (not in URL)
    const baseUrl: string = `${convexUrl}/telegram/webhook`;
    return baseUrl;
  },
});

export const testWebhookEndpoint = action({
  args: {},
  returns: v.object({
    success: v.boolean(),
    status: v.number(),
    url: v.string(),
    headers: v.any(),
    body: v.string(),
    message: v.string(),
  }),
  handler: async (ctx): Promise<{
    success: boolean;
    status: number;
    url: string;
    headers: Record<string, string>;
    body: string;
    message: string;
  }> => {
    try {
      console.log("🌐 Testing webhook endpoint accessibility...");
      const baseUrl: string = await ctx.runQuery(api.webhooks.getConvexSiteUrl, {});
      const webhookUrl: string = `${baseUrl}/telegram/webhook`;
      
      console.log("🌐 Testing URL:", webhookUrl);
      
      const response: Response = await fetch(webhookUrl, { 
        method: "GET",
        headers: {
          "User-Agent": "BuddyTasks-HealthCheck/1.0"
        }
      });
      
      const status: number = response.status;
      const headers: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        headers[key] = value;
      });
      let body: string = "";
      
      try {
        body = await response.text();
      } catch (e) {
        body = "Could not read response body";
      }
      
      console.log("🌐 Health check response:", { status, headers, body: body.substring(0, 200) });
      
      return {
        success: status >= 200 && status < 400,
        status,
        url: webhookUrl,
        headers,
        body: body.substring(0, 500), // Limit body size for display
        message: status === 200 
          ? "✅ Webhook endpoint is accessible" 
          : status === 404 
          ? "❌ Webhook endpoint not found (HTTP 404)" 
          : `⚠️ Unexpected status: HTTP ${status}`
      };
      
    } catch (error) {
      console.error("🌐 Health check failed:", error);
      return {
        success: false,
        status: 0,
        url: "",
        headers: {},
        body: "",
        message: `❌ Network error: ${error instanceof Error ? error.message : error}`
      };
    }
  },
});
