import { mutation, query, internalQuery, action } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";
import { internal } from "./_generated/api";
import { api } from "./_generated/api";

export const setSetting = mutation({
  args: {
    key: v.string(),
    value: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Use encryption for sensitive API keys
    const sensitiveKeys = [
      "OPENROUTER_API_KEY",
      "LINEAR_API_KEY", 
      "TELEGRAM_BOT_TOKEN",
      "TELEGRAM_WEBHOOK_SECRET"
    ];

    if (sensitiveKeys.includes(args.key)) {
      // Use encrypted storage for sensitive data
      await ctx.runMutation(internal.encryptionQueries.encryptSetting, {
        userId,
        key: args.key,
        value: args.value,
      });
    } else {
      // Use plain text for non-sensitive settings
      const existing = await ctx.db
        .query("settings")
        .withIndex("by_user_and_key", (q) => q.eq("userId", userId).eq("key", args.key))
        .first();

      if (existing) {
        await ctx.db.patch(existing._id, { 
          value: args.value,
          isEncrypted: false,
        });
      } else {
        await ctx.db.insert("settings", {
          userId,
          key: args.key,
          value: args.value,
          isEncrypted: false,
        });
      }
    }
  },
});

export const getSetting = internalQuery({
  args: {
    key: v.string(),
    userId: v.id("users"),
  },
  returns: v.union(v.string(), v.null()),
  handler: async (ctx, args): Promise<string | null> => {
    // Use decryption helper for all settings (handles both encrypted and plain text)
    return await ctx.runQuery(internal.encryptionQueries.decryptSetting, {
      userId: args.userId,
      key: args.key,
    });
  },
});

export const getPublicSetting = query({
  args: {
    key: v.string(),
  },
  returns: v.union(v.string(), v.null()),
  handler: async (ctx, args): Promise<string | null> => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    // Prevent access to sensitive keys from public queries
    const restrictedKeys = [
      "OPENROUTER_API_KEY",
      "LINEAR_API_KEY", 
      "TELEGRAM_BOT_TOKEN",
      "TELEGRAM_WEBHOOK_SECRET"
    ];

    if (restrictedKeys.includes(args.key)) {
      throw new Error("Access to this setting is restricted");
    }

    return await ctx.runQuery(internal.encryptionQueries.decryptSetting, {
      userId,
      key: args.key,
    });
  },
});

export const getAllSettings = query({
  args: {},
  returns: v.array(v.object({
    _id: v.id("settings"),
    _creationTime: v.number(),
    userId: v.id("users"),
    key: v.string(),
    value: v.string(),
    isEncrypted: v.boolean(),
  })),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const settings = await ctx.db
      .query("settings")
      .withIndex("by_user_and_key", (q) => q.eq("userId", userId))
      .collect();

    // Decrypt settings for display (sensitive values will be masked)
    const decryptedSettings = [];
    for (const setting of settings) {
      let value = setting.value;
      
      // Decrypt encrypted settings
      if (setting.isEncrypted) {
        try {
          value = await ctx.runQuery(internal.encryptionQueries.decryptSetting, {
            userId,
            key: setting.key,
          }) || "[DECRYPTION_ERROR]";
        } catch (error) {
          value = "[DECRYPTION_ERROR]";
        }
      }

      // Mask sensitive values for display
      const sensitiveKeys = [
        "OPENROUTER_API_KEY",
        "LINEAR_API_KEY", 
        "TELEGRAM_BOT_TOKEN",
        "TELEGRAM_WEBHOOK_SECRET"
      ];

      if (sensitiveKeys.includes(setting.key) && value && value !== "[DECRYPTION_ERROR]") {
        value = value.substring(0, 8) + "***" + value.substring(value.length - 4);
      }

      decryptedSettings.push({
        ...setting,
        value,
      });
    }

    return decryptedSettings;
  },
});

export const validateConfiguration = action({
  args: {},
  returns: v.object({
    keys: v.object({
      OPENROUTER_API_KEY: v.object({
        exists: v.boolean(),
        valid: v.boolean(),
        encrypted: v.boolean(),
        message: v.string(),
        details: v.optional(v.string()),
      }),
      LINEAR_API_KEY: v.object({
        exists: v.boolean(),
        valid: v.boolean(),
        encrypted: v.boolean(),
        message: v.string(),
        details: v.optional(v.string()),
      }),
      LINEAR_TEAM_ID: v.object({
        exists: v.boolean(),
        valid: v.boolean(),
        encrypted: v.boolean(),
        message: v.string(),
        details: v.optional(v.string()),
        teamName: v.optional(v.string()),
      }),
      TELEGRAM_BOT_TOKEN: v.object({
        exists: v.boolean(),
        valid: v.boolean(),
        encrypted: v.boolean(),
        message: v.string(),
        details: v.optional(v.string()),
        botUsername: v.optional(v.string()),
      }),
      TELEGRAM_WEBHOOK_SECRET: v.object({
        exists: v.boolean(),
        valid: v.boolean(),
        encrypted: v.boolean(),
        message: v.string(),
        details: v.optional(v.string()),
      }),
    }),
    encryptionStatus: v.object({
      enabled: v.boolean(),
      masterKeySet: v.boolean(),
    }),
    overallStatus: v.union(
      v.literal("complete"),
      v.literal("partial"),
      v.literal("error")
    ),
  }),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check encryption status
    const encryptionEnabled = process.env.ENCRYPTION_MASTER_KEY ? true : false;
    const encryptionStatus = {
      enabled: encryptionEnabled,
      masterKeySet: encryptionEnabled,
    };

    // Get all user settings upfront to avoid multiple queries
    const allUserSettings = await ctx.runQuery(api.settings.getAllSettings, {});
    
    // Helper to check if a setting exists and is encrypted  
    const checkSetting = (key: string) => {
      const setting = allUserSettings?.find((s: any) => s.key === key);
      return {
        exists: !!setting?.value,
        encrypted: setting?.isEncrypted || false,
        value: setting?.value,
      };
    };

    // Initialize results with proper typing
    const keys: {
      OPENROUTER_API_KEY: {
        exists: boolean;
        valid: boolean;
        encrypted: boolean;
        message: string;
        details?: string;
      };
      LINEAR_API_KEY: {
        exists: boolean;
        valid: boolean;
        encrypted: boolean;
        message: string;
        details?: string;
      };
      LINEAR_TEAM_ID: {
        exists: boolean;
        valid: boolean;
        encrypted: boolean;
        message: string;
        details?: string;
        teamName?: string;
      };
      TELEGRAM_BOT_TOKEN: {
        exists: boolean;
        valid: boolean;
        encrypted: boolean;
        message: string;
        details?: string;
        botUsername?: string;
      };
      TELEGRAM_WEBHOOK_SECRET: {
        exists: boolean;
        valid: boolean;
        encrypted: boolean;
        message: string;
        details?: string;
      };
    } = {
      OPENROUTER_API_KEY: {
        exists: false,
        valid: false,
        encrypted: false,
        message: "Not configured",
        details: undefined,
      },
      LINEAR_API_KEY: {
        exists: false,
        valid: false,
        encrypted: false,
        message: "Not configured",
        details: undefined,
      },
      LINEAR_TEAM_ID: {
        exists: false,
        valid: false,
        encrypted: false,
        message: "Not configured",
        details: undefined,
        teamName: undefined,
      },
      TELEGRAM_BOT_TOKEN: {
        exists: false,
        valid: false,
        encrypted: false,
        message: "Not configured",
        details: undefined,
        botUsername: undefined,
      },
      TELEGRAM_WEBHOOK_SECRET: {
        exists: false,
        valid: false,
        encrypted: false,
        message: "Not configured",
        details: undefined,
      },
    };

    // Validate OPENROUTER_API_KEY
    try {
      const openrouterSetting = checkSetting("OPENROUTER_API_KEY");
      keys.OPENROUTER_API_KEY.exists = openrouterSetting.exists;
      keys.OPENROUTER_API_KEY.encrypted = openrouterSetting.encrypted;

      if (openrouterSetting.exists) {
        const openrouterKey = await ctx.runQuery(internal.encryptionQueries.decryptSetting, {
          key: "OPENROUTER_API_KEY",
          userId,
        });

        if (openrouterKey) {
          // Basic format validation
          if (!openrouterKey.startsWith("sk-or-v1-")) {
            keys.OPENROUTER_API_KEY.message = "Invalid format";
            keys.OPENROUTER_API_KEY.details = "Should start with sk-or-v1-";
          } else {
            // Test API connection
            const response = await fetch("https://openrouter.ai/api/v1/models", {
              headers: {
                "Authorization": `Bearer ${openrouterKey}`,
              },
            });

            if (response.ok) {
              keys.OPENROUTER_API_KEY.valid = true;
              keys.OPENROUTER_API_KEY.message = "Valid and working";
              keys.OPENROUTER_API_KEY.details = "API key authenticated successfully";
            } else if (response.status === 401) {
              keys.OPENROUTER_API_KEY.message = "Invalid API key";
              keys.OPENROUTER_API_KEY.details = "Authentication failed";
            } else {
              keys.OPENROUTER_API_KEY.message = "API error";
              keys.OPENROUTER_API_KEY.details = `HTTP ${response.status}`;
            }
          }
        } else {
          keys.OPENROUTER_API_KEY.message = "Failed to decrypt";
        }
      }
    } catch (error) {
      keys.OPENROUTER_API_KEY.message = "Validation failed";
      keys.OPENROUTER_API_KEY.details = error instanceof Error ? error.message : "Unknown error";
    }

    // Validate LINEAR_API_KEY
    try {
      const linearKeySetting = checkSetting("LINEAR_API_KEY");
      keys.LINEAR_API_KEY.exists = linearKeySetting.exists;
      keys.LINEAR_API_KEY.encrypted = linearKeySetting.encrypted;

      if (linearKeySetting.exists) {
        const linearKey = await ctx.runQuery(internal.encryptionQueries.decryptSetting, {
          key: "LINEAR_API_KEY",
          userId,
        });

        if (linearKey) {
          // Basic format validation
          if (!linearKey.startsWith("lin_api_")) {
            keys.LINEAR_API_KEY.message = "Invalid format";
            keys.LINEAR_API_KEY.details = "Should start with lin_api_";
          } else {
            // Test API connection with simple viewer query
            const response = await fetch("https://api.linear.app/graphql", {
              method: "POST",
              headers: {
                "Authorization": linearKey,
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                query: `query { viewer { id name email } }`,
              }),
            });

            const data = await response.json();
            
            if (response.ok && !data.errors) {
              keys.LINEAR_API_KEY.valid = true;
              keys.LINEAR_API_KEY.message = "Valid and working";
              keys.LINEAR_API_KEY.details = `Connected as: ${data.data.viewer?.name || data.data.viewer?.email}`;
            } else if (response.status === 401 || data.errors?.[0]?.message?.includes("authentication")) {
              keys.LINEAR_API_KEY.message = "Invalid API key";
              keys.LINEAR_API_KEY.details = "Authentication failed";
            } else {
              keys.LINEAR_API_KEY.message = "API error";
              keys.LINEAR_API_KEY.details = data.errors?.[0]?.message || `HTTP ${response.status}`;
            }
          }
        } else {
          keys.LINEAR_API_KEY.message = "Failed to decrypt";
        }
      }
    } catch (error) {
      keys.LINEAR_API_KEY.message = "Validation failed";
      keys.LINEAR_API_KEY.details = error instanceof Error ? error.message : "Unknown error";
    }

    // Validate LINEAR_TEAM_ID (only if Linear API key is valid)
    try {
      const linearTeamIdSetting = checkSetting("LINEAR_TEAM_ID");
      keys.LINEAR_TEAM_ID.exists = linearTeamIdSetting.exists;
      keys.LINEAR_TEAM_ID.encrypted = linearTeamIdSetting.encrypted;

      if (linearTeamIdSetting.exists && keys.LINEAR_API_KEY.valid) {
        const linearTeamId = await ctx.runQuery(internal.encryptionQueries.decryptSetting, {
          key: "LINEAR_TEAM_ID",
          userId,
        });
        const linearKey = await ctx.runQuery(internal.encryptionQueries.decryptSetting, {
          key: "LINEAR_API_KEY",
          userId,
        });

        if (linearTeamId && linearKey) {
          const response = await fetch("https://api.linear.app/graphql", {
            method: "POST",
            headers: {
              "Authorization": linearKey,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              query: `query { team(id: "${linearTeamId}") { id name key } }`,
            }),
          });

          const data = await response.json();
          
          if (response.ok && !data.errors && data.data.team) {
            keys.LINEAR_TEAM_ID.valid = true;
            keys.LINEAR_TEAM_ID.message = "Valid team";
            keys.LINEAR_TEAM_ID.teamName = data.data.team.name;
          } else if (data.data?.team === null) {
            keys.LINEAR_TEAM_ID.message = "Team not found";
            keys.LINEAR_TEAM_ID.details = "Check team ID is correct";
          } else {
            keys.LINEAR_TEAM_ID.message = "Invalid team ID";
            keys.LINEAR_TEAM_ID.details = data.errors?.[0]?.message || "Team query failed";
          }
        } else {
          keys.LINEAR_TEAM_ID.message = "Failed to decrypt";
        }
      } else if (linearTeamIdSetting.exists && !keys.LINEAR_API_KEY.valid) {
        keys.LINEAR_TEAM_ID.message = "Requires valid Linear API key";
      }
    } catch (error) {
      keys.LINEAR_TEAM_ID.message = "Validation failed";
      keys.LINEAR_TEAM_ID.details = error instanceof Error ? error.message : "Unknown error";
    }

    // Validate TELEGRAM_BOT_TOKEN
    try {
      const telegramTokenSetting = checkSetting("TELEGRAM_BOT_TOKEN");
      keys.TELEGRAM_BOT_TOKEN.exists = telegramTokenSetting.exists;
      keys.TELEGRAM_BOT_TOKEN.encrypted = telegramTokenSetting.encrypted;

      if (telegramTokenSetting.exists) {
        const telegramToken = await ctx.runQuery(internal.encryptionQueries.decryptSetting, {
          key: "TELEGRAM_BOT_TOKEN",
          userId,
        });

        if (telegramToken) {
          // Basic format validation
          const telegramTokenPattern = /^\d{8,10}:[a-zA-Z0-9_-]{35}$/;
          if (!telegramTokenPattern.test(telegramToken)) {
            keys.TELEGRAM_BOT_TOKEN.message = "Invalid format";
            keys.TELEGRAM_BOT_TOKEN.details = "Should be format: 123456789:ABCDEF...";
          } else {
            // Test bot token
            const response = await fetch(`https://api.telegram.org/bot${telegramToken}/getMe`);
            const data = await response.json();

            if (response.ok && data.ok) {
              keys.TELEGRAM_BOT_TOKEN.valid = true;
              keys.TELEGRAM_BOT_TOKEN.message = "Valid bot token";
              keys.TELEGRAM_BOT_TOKEN.botUsername = data.result.username;
            } else {
              keys.TELEGRAM_BOT_TOKEN.message = "Invalid bot token";
              keys.TELEGRAM_BOT_TOKEN.details = data.description || "Token authentication failed";
            }
          }
        } else {
          keys.TELEGRAM_BOT_TOKEN.message = "Failed to decrypt";
        }
      }
    } catch (error) {
      keys.TELEGRAM_BOT_TOKEN.message = "Validation failed";
      keys.TELEGRAM_BOT_TOKEN.details = error instanceof Error ? error.message : "Unknown error";
    }

    // Validate TELEGRAM_WEBHOOK_SECRET
    try {
      const webhookSecretSetting = checkSetting("TELEGRAM_WEBHOOK_SECRET");
      keys.TELEGRAM_WEBHOOK_SECRET.exists = webhookSecretSetting.exists;
      keys.TELEGRAM_WEBHOOK_SECRET.encrypted = webhookSecretSetting.encrypted;

      if (webhookSecretSetting.exists) {
        const webhookSecret = await ctx.runQuery(internal.encryptionQueries.decryptSetting, {
          key: "TELEGRAM_WEBHOOK_SECRET",
          userId,
        });

        if (webhookSecret) {
          const trimmed = webhookSecret.trim();
          const allowed = /^[A-Za-z0-9_-]+$/.test(trimmed);
          if (!allowed) {
            keys.TELEGRAM_WEBHOOK_SECRET.message = "Invalid characters";
            keys.TELEGRAM_WEBHOOK_SECRET.details = "Allowed: A–Z, a–z, 0–9, _ and -";
          } else if (trimmed.length < 32) {
            keys.TELEGRAM_WEBHOOK_SECRET.message = "Too short (min 32 chars)";
          } else if (trimmed.length > 256) {
            keys.TELEGRAM_WEBHOOK_SECRET.message = "Too long (max 256 chars)";
          } else {
            keys.TELEGRAM_WEBHOOK_SECRET.valid = true;
            keys.TELEGRAM_WEBHOOK_SECRET.message = "Valid webhook secret";
          }
        } else {
          keys.TELEGRAM_WEBHOOK_SECRET.message = "Failed to decrypt";
        }
      }
    } catch (error) {
      keys.TELEGRAM_WEBHOOK_SECRET.message = "Validation failed";
      keys.TELEGRAM_WEBHOOK_SECRET.details = error instanceof Error ? error.message : "Unknown error";
    }

    // Calculate overall status
    const allKeys = Object.values(keys);
    const validCount = allKeys.filter(k => k.valid).length;
    const existsCount = allKeys.filter(k => k.exists).length;
    
    let overallStatus: "complete" | "partial" | "error";
    if (validCount === allKeys.length) {
      overallStatus = "complete";
    } else if (existsCount > 0) {
      overallStatus = "partial";
    } else {
      overallStatus = "error";
    }

    return {
      keys,
      encryptionStatus,
      overallStatus,
    };
  },
});
