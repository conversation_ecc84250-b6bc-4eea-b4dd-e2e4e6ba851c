import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";
import { internal } from "./_generated/api";

// Security dashboard queries (admin/debugging)
export const getSecurityStats = query({
  args: {},
  returns: v.object({
    totalSecurityEvents: v.number(),
    criticalEvents: v.number(),
    rateLimitEvents: v.number(),
    authFailures: v.number(),
    suspiciousRequests: v.number(),
    last24Hours: v.object({
      total: v.number(),
      critical: v.number(),
    }),
  }),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const allEvents = await ctx.db.query("securityLogs").collect();
    const now = Date.now();
    const last24Hours = allEvents.filter(e => e.timestamp > now - 24 * 60 * 60 * 1000);

    return {
      totalSecurityEvents: allEvents.length,
      criticalEvents: allEvents.filter(e => e.severity === "critical").length,
      rateLimitEvents: allEvents.filter(e => e.eventType === "rate_limit_exceeded").length,
      authFailures: allEvents.filter(e => e.eventType === "auth_failure").length,
      suspiciousRequests: allEvents.filter(e => e.eventType === "suspicious_request").length,
      last24Hours: {
        total: last24Hours.length,
        critical: last24Hours.filter(e => e.severity === "critical").length,
      },
    };
  },
});

export const getUserSecurityEvents = query({
  args: {
    limit: v.optional(v.number()),
  },
  returns: v.array(v.object({
    _id: v.id("securityLogs"),
    _creationTime: v.number(),
    userId: v.optional(v.id("users")),
    timestamp: v.number(),
    eventType: v.union(
      v.literal("auth_failure"),
      v.literal("rate_limit_exceeded"),
      v.literal("invalid_signature"),
      v.literal("suspicious_request"),
      v.literal("data_access"),
      v.literal("setting_change"),
      v.literal("encryption_error")
    ),
    details: v.string(),
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),
    requestUrl: v.optional(v.string()),
    severity: v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("critical")
    ),
  })),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const result: any = await ctx.runQuery(internal.securityQueries.getRecentSecurityEvents, {
      userId,
      limit: args.limit,
    });
    return result;
  },
});

export const getEncryptionStatus = query({
  args: {},
  returns: v.object({
    encryptedSettings: v.number(),
    plaintextSettings: v.number(),
    encryptionEnabled: v.boolean(),
    lastMigration: v.optional(v.number()),
  }),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const userSettings = await ctx.db
      .query("settings")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();

    const encryptedCount = userSettings.filter(s => s.isEncrypted).length;
    const plaintextCount = userSettings.filter(s => !s.isEncrypted).length;

    return {
      encryptedSettings: encryptedCount,
      plaintextSettings: plaintextCount,
      encryptionEnabled: true, // We'll assume it's enabled if we get here
      lastMigration: undefined, // Could track this in a separate table
    };
  },
});

// Check system security status
export const getSystemSecurityStatus = query({
  args: {},
  returns: v.object({
    encryptionEnabled: v.boolean(),
    webhookSecurityEnabled: v.boolean(),
    rateLimitingEnabled: v.boolean(),
    auditLoggingEnabled: v.boolean(),
    securityHeaders: v.boolean(),
    recommendations: v.array(v.string()),
    overallScore: v.number(), // 0-100
  }),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const encryptionEnabled = true; // Assume enabled in production
    const webhookSecurityEnabled = true; // Assume enabled
    const rateLimitingEnabled = true; // Always enabled in our implementation
    const auditLoggingEnabled = true; // Always enabled
    const securityHeaders = true; // Always enabled

    const recommendations: string[] = [];
    let score = 0;

    if (encryptionEnabled) {
      score += 30;
    } else {
      recommendations.push("Enable encryption by setting ENCRYPTION_MASTER_KEY environment variable");
    }

    if (webhookSecurityEnabled) {
      score += 20;
    } else {
      recommendations.push("Set WEBHOOK_SECRET environment variable for webhook security");
    }

    if (rateLimitingEnabled) score += 20;
    if (auditLoggingEnabled) score += 15;
    if (securityHeaders) score += 15;

    // Check for recent security events
    const recentEvents = await ctx.runQuery(internal.securityQueries.getRecentSecurityEvents, {
      userId,
      severity: "critical",
      limit: 10,
    });

    if (recentEvents.length > 0) {
      score -= 10;
      recommendations.push(`You have ${recentEvents.length} critical security events in the last 50 logs`);
    }

    if (score >= 90) {
      recommendations.push("Excellent security posture! Consider regular security audits.");
    } else if (score >= 70) {
      recommendations.push("Good security setup, but there's room for improvement.");
    } else {
      recommendations.push("Security needs immediate attention. Please address the recommendations above.");
    }

    return {
      encryptionEnabled,
      webhookSecurityEnabled,
      rateLimitingEnabled,
      auditLoggingEnabled,
      securityHeaders,
      recommendations,
      overallScore: Math.max(0, score),
    };
  },
});