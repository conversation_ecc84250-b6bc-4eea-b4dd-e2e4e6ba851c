// Retry utilities for handling transient failures

export interface RetryOptions {
  maxAttempts: number;
  baseDelayMs: number;
  maxDelayMs: number;
  backoffMultiplier: number;
}

export const DEFAULT_RETRY_OPTIONS: RetryOptions = {
  maxAttempts: 3,
  baseDelayMs: 1000,
  maxDelayMs: 30000,
  backoffMultiplier: 2,
};

export class RetryableError extends Error {
  constructor(message: string, public readonly isRetryable: boolean = true) {
    super(message);
    this.name = 'RetryableError';
  }
}

export function calculateDelay(attempt: number, options: RetryOptions): number {
  const delay = options.baseDelayMs * Math.pow(options.backoffMultiplier, attempt - 1);
  return Math.min(delay, options.maxDelayMs);
}

export function isRetryableError(error: unknown): boolean {
  if (error instanceof RetryableError) {
    return error.isRetryable;
  }
  
  if (error instanceof Error) {
    const message = error.message.toLowerCase();
    
    // Network/timeout errors are retryable
    if (message.includes('timeout') || 
        message.includes('network') || 
        message.includes('connection') ||
        message.includes('fetch')) {
      return true;
    }
    
    // HTTP 5xx errors are retryable
    if (message.includes('500') || 
        message.includes('502') || 
        message.includes('503') || 
        message.includes('504')) {
      return true;
    }
    
    // Rate limiting is retryable
    if (message.includes('429') || message.includes('rate limit')) {
      return true;
    }
    
    // OpenRouter specific errors that are retryable
    if (message.includes('openrouter api error: 5')) {
      return true;
    }
    
    // Linear API errors that are retryable
    if (message.includes('linear api error: 5')) {
      return true;
    }
  }
  
  return false;
}

export async function withRetry<T>(
  operation: () => Promise<T>,
  options: Partial<RetryOptions> = {}
): Promise<T> {
  const opts = { ...DEFAULT_RETRY_OPTIONS, ...options };
  let lastError: unknown;
  
  for (let attempt = 1; attempt <= opts.maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      console.log(`🔄 Attempt ${attempt}/${opts.maxAttempts} failed:`, error);
      
      // Don't retry if it's the last attempt or error is not retryable
      if (attempt === opts.maxAttempts || !isRetryableError(error)) {
        break;
      }
      
      // Wait before retrying
      const delay = calculateDelay(attempt, opts);
      console.log(`⏳ Waiting ${delay}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}

// Specific retry configurations for different services
export const OPENROUTER_RETRY_OPTIONS: RetryOptions = {
  maxAttempts: 3,
  baseDelayMs: 2000,
  maxDelayMs: 30000,
  backoffMultiplier: 2,
};

export const LINEAR_RETRY_OPTIONS: RetryOptions = {
  maxAttempts: 3,
  baseDelayMs: 1000,
  maxDelayMs: 15000,
  backoffMultiplier: 2,
};

export const TELEGRAM_RETRY_OPTIONS: RetryOptions = {
  maxAttempts: 2,
  baseDelayMs: 1000,
  maxDelayMs: 5000,
  backoffMultiplier: 2,
};
