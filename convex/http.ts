import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";
import { auth } from "./auth";
import { api, internal } from "./_generated/api";
import { Id } from "./_generated/dataModel";
import { checkRateLimit, detectSuspiciousActivity, buildSecurityHeaders } from "./security";
import { getWebhookSecretFromRequest, validateRequestSize, extractClientIP } from "./validation";

const http = httpRouter();

// Add authentication routes
auth.addHttpRoutes(http);

// Test route
http.route({
  path: "/test",
  method: "GET",
  handler: httpAction(async (ctx, request) => {
    return new Response(JSON.stringify({ 
      status: "success", 
      message: "HTTP routes are working!",
      timestamp: new Date().toISOString(),
      url: request.url 
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  }),
});

// Webhook health check (GET)
http.route({
  path: "/telegram/webhook",
  method: "GET",
  handler: httpAction(async (ctx, request) => {
    const headers = { "Content-Type": "application/json", ...buildSecurityHeaders() };
    return new Response(JSON.stringify({ 
      status: "webhook_ready", 
      message: "Telegram webhook endpoint is accessible",
      timestamp: new Date().toISOString(),
      method: "GET"
    }), {
      status: 200,
      headers,
    });
  }),
});

// Telegram webhook endpoint (POST) - simplified for initial testing
http.route({
  path: "/telegram/webhook",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    const headers = { "Content-Type": "application/json", ...buildSecurityHeaders() };
    console.log("🎯 Webhook POST endpoint hit!");

    try {
      // Basic request guards
      const okSize = validateRequestSize(request.headers.get("content-length"), 1024 * 1024);
      const ip = extractClientIP(request);
      const ua = request.headers.get("user-agent") || "unknown";
      if (!okSize) {
        await ctx.runMutation(internal.securityQueries.logSecurityEvent, {
          userId: undefined,
          eventType: "suspicious_request",
          details: "Payload too large",
          ipAddress: ip,
          userAgent: ua,
          requestUrl: request.url,
          severity: "medium",
        });
        await ctx.runMutation(api.webhooks.logWebhookRequest, {
          method: "POST",
          path: "/telegram/webhook",
          body: "[REQUEST_TOO_LARGE]",
          status: "error",
        });
        return new Response(JSON.stringify({ error: "Request too large" }), { status: 413, headers });
      }

      const rl = checkRateLimit(`telegram:${ip}`, 60, 60_000);
      if (!rl.allowed) {
        await ctx.runMutation(internal.securityQueries.logSecurityEvent, {
          userId: undefined,
          eventType: "rate_limit_exceeded",
          details: `Rate limit exceeded for IP ${ip}`,
          ipAddress: ip,
          userAgent: ua,
          requestUrl: request.url,
          severity: "medium",
        });
        await ctx.runMutation(api.webhooks.logWebhookRequest, {
          method: "POST",
          path: "/telegram/webhook",
          body: "[RATE_LIMITED]",
          status: "error",
        });
        return new Response(JSON.stringify({ error: "Too Many Requests" }), { status: 429, headers });
      }

      const suspicion = await detectSuspiciousActivity(ip, ua, new URL(request.url).pathname);
      if (suspicion.suspicious) {
        await ctx.runMutation(internal.securityQueries.logSecurityEvent, {
          userId: undefined,
          eventType: "suspicious_request",
          details: suspicion.reason || "Suspicious request detected",
          ipAddress: ip,
          userAgent: ua,
          requestUrl: request.url,
          severity: "high",
        });
        await ctx.runMutation(api.webhooks.logWebhookRequest, {
          method: "POST",
          path: "/telegram/webhook",
          body: "[SUSPICIOUS_REQUEST]",
          status: "error",
        });
        return new Response(JSON.stringify({ error: "Bad Request" }), { status: 400, headers });
      }

      const body = await request.text();
      console.log("📥 Received webhook body:", body.substring(0, 200));

      // Parse JSON
      let data;
      try {
        data = JSON.parse(body);
      } catch (parseError) {
        console.error("❌ Invalid JSON:", parseError);
        await ctx.runMutation(api.webhooks.logWebhookRequest, {
          method: "POST",
          path: "/telegram/webhook",
          body: body.slice(0, 1000),
          status: "error",
        });
        return new Response(JSON.stringify({ error: "Invalid JSON" }), { status: 400, headers });
      }

      // Extract webhook secret (header preferred; query fallback for dev)
      const { secret: webhookSecret } = getWebhookSecretFromRequest(request);
      if (!webhookSecret) {
        console.error("❌ Missing webhook secret header");
        await ctx.runMutation(api.webhooks.logWebhookRequest, {
          method: "POST",
          path: "/telegram/webhook",
          body: body.slice(0, 1000),
          status: "error",
        });
        return new Response(JSON.stringify({ error: "Missing webhook secret" }), { status: 401, headers });
      }

      // Find user by webhook secret
      const userId: Id<"users"> | null = await ctx.runQuery(internal.telegram.findUserByWebhookSecret, {
        webhookSecret,
      });

      if (!userId) {
        console.error("❌ Invalid webhook secret");
        await ctx.runMutation(internal.securityQueries.logSecurityEvent, {
          userId: undefined,
          eventType: "invalid_signature",
          details: "Webhook secret did not match any user",
          ipAddress: ip,
          userAgent: ua,
          requestUrl: request.url,
          severity: "high",
        });
        await ctx.runMutation(api.webhooks.logWebhookRequest, {
          method: "POST",
          path: "/telegram/webhook",
          body: body.slice(0, 1000),
          status: "error",
        });
        return new Response(JSON.stringify({ error: "Invalid webhook secret" }), { status: 401, headers });
      }

      console.log("✅ Authenticated user:", userId);

      // Get bot token for processing
      const botToken = await ctx.runQuery(internal.settings.getSetting, {
        key: "TELEGRAM_BOT_TOKEN",
        userId,
      });

      if (!botToken) {
        console.error("❌ Bot token not configured");
        await ctx.runMutation(api.webhooks.logWebhookRequest, {
          method: "POST",
          path: "/telegram/webhook",
          body: body.slice(0, 1000),
          status: "error",
          userId,
        });
        return new Response(JSON.stringify({ error: "Bot not configured" }), { status: 500, headers });
      }

      // Acknowledge immediately (Telegram expects quick response)
      const response = new Response(JSON.stringify({ ok: true }), { status: 200, headers });

      // Process message in background if it exists
      if (data.message && data.message.text) {
        console.log("🚀 Scheduling background message processing");
        ctx.scheduler.runAfter(0, internal.telegram.processIncomingMessageAsync, {
          messageId: data.message.message_id,
          text: data.message.text,
          date: data.message.date,
          chatId: data.message.chat.id,
          botToken,
        });
      }
      // Log success webhook receipt
      await ctx.runMutation(api.webhooks.logWebhookRequest, {
        method: "POST",
        path: "/telegram/webhook",
        body: (typeof body === "string" ? body : "").slice(0, 1000),
        status: "success",
        userId,
      });

      return response;

    } catch (error) {
      console.error("❌ Webhook error:", error);
      try {
        await ctx.runMutation(api.webhooks.logWebhookRequest, {
          method: "POST",
          path: "/telegram/webhook",
          body: "[EXCEPTION]",
          status: "error",
        });
      } catch {}
      return new Response(JSON.stringify({ 
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error"
      }), { status: 500, headers });
    }
  }),
});

export default http;
