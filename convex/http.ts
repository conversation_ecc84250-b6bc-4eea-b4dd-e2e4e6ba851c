import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";
import { auth } from "./auth";
import { api, internal } from "./_generated/api";
import { Id } from "./_generated/dataModel";
import { checkRateLimit, checkUserRateLimit, detectSuspiciousActivity, buildSecurityHeaders } from "./security";
import { validateRequestSize, extractClientIP } from "./validation";

const http = httpRouter();

// Add authentication routes
auth.addHttpRoutes(http);

// Test route
http.route({
  path: "/test",
  method: "GET",
  handler: httpAction(async (ctx, request) => {
    return new Response(JSON.stringify({ 
      status: "success", 
      message: "HTTP routes are working!",
      timestamp: new Date().toISOString(),
      url: request.url 
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  }),
});

// Webhook health check (GET)
http.route({
  path: "/telegram/webhook",
  method: "GET",
  handler: httpAction(async (ctx, request) => {
    const headers = { "Content-Type": "application/json", ...buildSecurityHeaders() };
    return new Response(JSON.stringify({ 
      status: "webhook_ready", 
      message: "Telegram webhook endpoint is accessible",
      timestamp: new Date().toISOString(),
      method: "GET"
    }), {
      status: 200,
      headers,
    });
  }),
});

// Telegram webhook endpoint (POST) - simplified for initial testing
http.route({
  path: "/telegram/webhook",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    const headers = { "Content-Type": "application/json", ...buildSecurityHeaders() };
    console.log("🎯 Webhook POST endpoint hit!");

    try {
      // Basic request guards
      const okSize = validateRequestSize(request.headers.get("content-length"), 1024 * 1024);
      const ip = extractClientIP(request);
      const ua = request.headers.get("user-agent") || "unknown";
      if (!okSize) {
        await ctx.runMutation(internal.securityQueries.logSecurityEvent, {
          userId: undefined,
          eventType: "suspicious_request",
          details: "Payload too large",
          ipAddress: ip,
          userAgent: ua,
          requestUrl: request.url,
          severity: "medium",
        });
        await ctx.runMutation(api.webhooks.logWebhookRequest, {
          method: "POST",
          path: "/telegram/webhook",
          body: "[REQUEST_TOO_LARGE]",
          status: "error",
        });
        return new Response(JSON.stringify({ error: "Request too large" }), { status: 413, headers });
      }

      const rl = checkRateLimit(`telegram:${ip}`, 60, 60_000);
      if (!rl.allowed) {
        await ctx.runMutation(internal.securityQueries.logSecurityEvent, {
          userId: undefined,
          eventType: "rate_limit_exceeded",
          details: `Rate limit exceeded for IP ${ip}`,
          ipAddress: ip,
          userAgent: ua,
          requestUrl: request.url,
          severity: "medium",
        });
        await ctx.runMutation(api.webhooks.logWebhookRequest, {
          method: "POST",
          path: "/telegram/webhook",
          body: "[RATE_LIMITED]",
          status: "error",
        });
        return new Response(JSON.stringify({ error: "Too Many Requests" }), { status: 429, headers });
      }

      const suspicion = await detectSuspiciousActivity(ip, ua, new URL(request.url).pathname);
      if (suspicion.suspicious) {
        await ctx.runMutation(internal.securityQueries.logSecurityEvent, {
          userId: undefined,
          eventType: "suspicious_request",
          details: suspicion.reason || "Suspicious request detected",
          ipAddress: ip,
          userAgent: ua,
          requestUrl: request.url,
          severity: "high",
        });
        await ctx.runMutation(api.webhooks.logWebhookRequest, {
          method: "POST",
          path: "/telegram/webhook",
          body: "[SUSPICIOUS_REQUEST]",
          status: "error",
        });
        return new Response(JSON.stringify({ error: "Bad Request" }), { status: 400, headers });
      }

      const body = await request.text();
      console.log("📥 Received webhook body:", body.substring(0, 200));

      // Parse and validate JSON
      let data;
      try {
        data = JSON.parse(body);

        // Basic Telegram webhook structure validation
        if (!data || typeof data !== 'object') {
          throw new Error("Invalid webhook data structure");
        }

        // Validate required Telegram webhook fields
        if (data.message) {
          if (!data.message.message_id || !data.message.date || !data.message.chat) {
            throw new Error("Invalid message structure");
          }

          // Validate message text length (Telegram max is 4096 characters)
          if (data.message.text && data.message.text.length > 4096) {
            throw new Error("Message text too long");
          }

          // Validate chat ID
          if (!Number.isInteger(data.message.chat.id)) {
            throw new Error("Invalid chat ID");
          }
        }

      } catch (parseError) {
        console.error("❌ Invalid webhook data:", parseError);
        await ctx.runMutation(api.webhooks.logWebhookRequest, {
          method: "POST",
          path: "/telegram/webhook",
          body: body.slice(0, 1000),
          status: "error",
        });
        return new Response(JSON.stringify({ error: "Invalid webhook data" }), { status: 400, headers });
      }

      // Validate Telegram webhook signature using proper X-Telegram-Bot-Api-Secret-Token
      const telegramSecretToken = request.headers.get('X-Telegram-Bot-Api-Secret-Token');
      if (!telegramSecretToken) {
        console.error("❌ Missing X-Telegram-Bot-Api-Secret-Token header");
        await ctx.runMutation(api.webhooks.logWebhookRequest, {
          method: "POST",
          path: "/telegram/webhook",
          body: body.slice(0, 1000),
          status: "error",
        });
        return new Response(JSON.stringify({ error: "Missing Telegram secret token" }), { status: 401, headers });
      }

      // Log the chat ID for debugging
      try {
        if (data.message?.chat) {
          const chatId = data.message.chat.id;
          console.log("📥 Received message for chat ID:", chatId);
        }
      } catch (extractError) {
        console.log("⚠️ Could not extract chat info from webhook data");
      }

      // Find user by validating the secret token against their stored webhook secret
      const userId: Id<"users"> | null = await ctx.runQuery(internal.telegram.findUserByTelegramSecret, {
        telegramSecretToken,
      });

      if (!userId) {
        console.error("❌ Invalid Telegram secret token");
        await ctx.runMutation(internal.securityQueries.logSecurityEvent, {
          userId: undefined,
          eventType: "invalid_signature",
          details: "Telegram secret token did not match any user",
          ipAddress: ip,
          userAgent: ua,
          requestUrl: request.url,
          severity: "high",
        });
        await ctx.runMutation(api.webhooks.logWebhookRequest, {
          method: "POST",
          path: "/telegram/webhook",
          body: body.slice(0, 1000),
          status: "error",
        });
        return new Response(JSON.stringify({ error: "Invalid Telegram secret token" }), { status: 401, headers });
      }

      console.log("✅ Authenticated user:", userId);

      // Apply user-specific rate limiting
      const userRateLimit = checkUserRateLimit(userId, 'webhook');
      if (!userRateLimit.allowed) {
        await ctx.runMutation(internal.securityQueries.logSecurityEvent, {
          userId,
          eventType: "rate_limit_exceeded",
          details: `User webhook rate limit exceeded`,
          ipAddress: ip,
          userAgent: ua,
          requestUrl: request.url,
          severity: "medium",
        });
        await ctx.runMutation(api.webhooks.logWebhookRequest, {
          method: "POST",
          path: "/telegram/webhook",
          body: "[USER_RATE_LIMITED]",
          status: "error",
          userId,
        });
        return new Response(JSON.stringify({
          error: "Rate limit exceeded",
          resetTime: userRateLimit.resetTime
        }), { status: 429, headers });
      }

      // Get bot token for processing
      const botToken = await ctx.runQuery(internal.settings.getSetting, {
        key: "TELEGRAM_BOT_TOKEN",
        userId,
      });

      if (!botToken) {
        console.error("❌ Bot token not configured");
        await ctx.runMutation(api.webhooks.logWebhookRequest, {
          method: "POST",
          path: "/telegram/webhook",
          body: body.slice(0, 1000),
          status: "error",
          userId,
        });
        return new Response(JSON.stringify({ error: "Bot not configured" }), { status: 500, headers });
      }

      // Acknowledge immediately (Telegram expects quick response)
      const response = new Response(JSON.stringify({ ok: true }), { status: 200, headers });

      // Process message in background if it exists
      if (data.message && data.message.text) {
        console.log("🚀 Scheduling background message processing");
        ctx.scheduler.runAfter(0, internal.telegram.processIncomingMessageAsync, {
          messageId: data.message.message_id,
          text: data.message.text,
          date: data.message.date,
          chatId: data.message.chat.id,
          botToken,
        });
      }
      // Log success webhook receipt
      await ctx.runMutation(api.webhooks.logWebhookRequest, {
        method: "POST",
        path: "/telegram/webhook",
        body: (typeof body === "string" ? body : "").slice(0, 1000),
        status: "success",
        userId,
      });

      return response;

    } catch (error) {
      console.error("❌ Webhook error:", error);
      try {
        await ctx.runMutation(api.webhooks.logWebhookRequest, {
          method: "POST",
          path: "/telegram/webhook",
          body: "[EXCEPTION]",
          status: "error",
        });
      } catch {}
      return new Response(JSON.stringify({ 
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error"
      }), { status: 500, headers });
    }
  }),
});

export default http;
