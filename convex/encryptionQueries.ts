import { internalQuery, internalMutation } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
import { internal } from "./_generated/api";

// Core encryption functions (not using Node.js)
async function encryptData(
  plaintext: string,
  masterKey: string,
  userId: Id<"users">
): Promise<{
  ciphertext: string;
  salt: string;
  iv: string;
  tag: string;
}> {
  if (!masterKey) {
    throw new Error("Encryption master key not configured");
  }

  // Use user ID as part of salt for user-specific encryption
  const userSalt = new TextEncoder().encode(`${userId}_salt`);
  const salt = new Uint8Array(16);
  crypto.getRandomValues(salt);
  
  // Combine user-specific salt with random salt
  const combinedSalt = new Uint8Array(32);
  combinedSalt.set(userSalt.slice(0, 16), 0);
  combinedSalt.set(salt, 16);
  
  // Derive key using PBKDF2
  const keyMaterial = await crypto.subtle.importKey(
    "raw",
    new TextEncoder().encode(masterKey),
    "PBKDF2",
    false,
    ["deriveBits", "deriveKey"]
  );
  
  const derivedKey = await crypto.subtle.deriveKey(
    {
      name: "PBKDF2",
      salt: combinedSalt,
      iterations: 100000,
      hash: "SHA-256",
    },
    keyMaterial,
    { name: "AES-GCM", length: 256 },
    false,
    ["encrypt", "decrypt"]
  );
  
  // Generate random IV
  const iv = new Uint8Array(12);
  crypto.getRandomValues(iv);
  
  // Encrypt the data
  const encodedText = new TextEncoder().encode(plaintext);
  const encrypted = await crypto.subtle.encrypt(
    {
      name: "AES-GCM",
      iv: iv,
    },
    derivedKey,
    encodedText
  );
  
  // Split the result to get ciphertext and authentication tag
  const encryptedArray = new Uint8Array(encrypted);
  const ciphertext = encryptedArray.slice(0, -16);
  const tag = encryptedArray.slice(-16);
  
  return {
    ciphertext: Array.from(ciphertext)
      .map(b => b.toString(16).padStart(2, '0'))
      .join(''),
    salt: Array.from(salt)
      .map(b => b.toString(16).padStart(2, '0'))
      .join(''),
    iv: Array.from(iv)
      .map(b => b.toString(16).padStart(2, '0'))
      .join(''),
    tag: Array.from(tag)
      .map(b => b.toString(16).padStart(2, '0'))
      .join(''),
  };
}

async function decryptData(
  encryptedData: {
    ciphertext: string;
    salt: string;
    iv: string;
    tag: string;
  },
  masterKey: string,
  userId: Id<"users">
): Promise<string> {
  if (!masterKey) {
    throw new Error("Encryption master key not configured");
  }

  // Convert hex strings back to Uint8Arrays
  const ciphertext = new Uint8Array(
    encryptedData.ciphertext.match(/.{2}/g)?.map(byte => parseInt(byte, 16)) || []
  );
  const salt = new Uint8Array(
    encryptedData.salt.match(/.{2}/g)?.map(byte => parseInt(byte, 16)) || []
  );
  const iv = new Uint8Array(
    encryptedData.iv.match(/.{2}/g)?.map(byte => parseInt(byte, 16)) || []
  );
  const tag = new Uint8Array(
    encryptedData.tag.match(/.{2}/g)?.map(byte => parseInt(byte, 16)) || []
  );
  
  // Reconstruct the combined salt
  const userSalt = new TextEncoder().encode(`${userId}_salt`);
  const combinedSalt = new Uint8Array(32);
  combinedSalt.set(userSalt.slice(0, 16), 0);
  combinedSalt.set(salt, 16);
  
  // Derive key using PBKDF2
  const keyMaterial = await crypto.subtle.importKey(
    "raw",
    new TextEncoder().encode(masterKey),
    "PBKDF2",
    false,
    ["deriveBits", "deriveKey"]
  );
  
  const derivedKey = await crypto.subtle.deriveKey(
    {
      name: "PBKDF2",
      salt: combinedSalt,
      iterations: 100000,
      hash: "SHA-256",
    },
    keyMaterial,
    { name: "AES-GCM", length: 256 },
    false,
    ["encrypt", "decrypt"]
  );
  
  // Combine ciphertext and tag for decryption
  const encryptedWithTag = new Uint8Array(ciphertext.length + tag.length);
  encryptedWithTag.set(ciphertext, 0);
  encryptedWithTag.set(tag, ciphertext.length);
  
  // Decrypt the data
  const decrypted = await crypto.subtle.decrypt(
    {
      name: "AES-GCM",
      iv: iv,
    },
    derivedKey,
    encryptedWithTag
  );
  
  return new TextDecoder().decode(decrypted);
}

// Store encrypted setting
export const encryptSetting = internalMutation({
  args: {
    userId: v.id("users"),
    key: v.string(),
    value: v.string(),
  },
  handler: async (ctx, args) => {
    const masterKey = process.env.ENCRYPTION_MASTER_KEY;
    if (!masterKey) {
      throw new Error("Encryption master key not configured");
    }

    try {
      const encrypted = await encryptData(args.value, masterKey, args.userId);
      
      // Store encrypted data
      const existing = await ctx.db
        .query("settings")
        .withIndex("by_user_and_key", (q) => 
          q.eq("userId", args.userId).eq("key", args.key)
        )
        .first();
        
      if (existing) {
        await ctx.db.patch(existing._id, {
          value: JSON.stringify(encrypted),
          isEncrypted: true,
        });
      } else {
        await ctx.db.insert("settings", {
          userId: args.userId,
          key: args.key,
          value: JSON.stringify(encrypted),
          isEncrypted: true,
        });
      }
    } catch (error) {
      console.error("Encryption failed:", error);
      throw new Error("Failed to encrypt setting");
    }
  },
});

// Retrieve and decrypt setting
export const decryptSetting = internalQuery({
  args: {
    userId: v.id("users"),
    key: v.string(),
  },
  returns: v.union(v.string(), v.null()),
  handler: async (ctx, args): Promise<string | null> => {
    const setting = await ctx.db
      .query("settings")
      .withIndex("by_user_and_key", (q) => 
        q.eq("userId", args.userId).eq("key", args.key)
      )
      .first();
      
    if (!setting) {
      return null;
    }
    
    // If not encrypted, return as-is (for backward compatibility)
    if (!setting.isEncrypted) {
      return setting.value;
    }
    
    const masterKey = process.env.ENCRYPTION_MASTER_KEY;
    if (!masterKey) {
      throw new Error("Encryption master key not configured");
    }
    
    try {
      const encryptedData = JSON.parse(setting.value);
      return await decryptData(encryptedData, masterKey, args.userId);
    } catch (error) {
      console.error("Decryption failed:", error);
      return null;
    }
  },
});

// Get setting info without decrypting (for validation purposes)
export const getSettingInfo = internalQuery({
  args: {
    userId: v.id("users"),
    key: v.string(),
  },
  returns: v.union(v.object({
    value: v.string(),
    isEncrypted: v.boolean(),
    _id: v.id("settings"),
    _creationTime: v.number(),
    userId: v.id("users"),
    key: v.string(),
  }), v.null()),
  handler: async (ctx, args) => {
    const setting = await ctx.db
      .query("settings")
      .withIndex("by_user_and_key", (q) => 
        q.eq("userId", args.userId).eq("key", args.key)
      )
      .first();
      
    return setting || null;
  },
});

// Migrate a single setting to encrypted format
export const migrateSettingToEncrypted = internalMutation({
  args: {
    settingId: v.id("settings"),
  },
  handler: async (ctx, args) => {
    const setting = await ctx.db.get(args.settingId);
    if (!setting || setting.isEncrypted) {
      return; // Already encrypted or doesn't exist
    }
    
    const masterKey = process.env.ENCRYPTION_MASTER_KEY;
    if (!masterKey) {
      throw new Error("Encryption master key not configured");
    }
    
    try {
      const encrypted = await encryptData(setting.value, masterKey, setting.userId);
      
      await ctx.db.patch(args.settingId, {
        value: JSON.stringify(encrypted),
        isEncrypted: true,
      });
    } catch (error) {
      console.error("Migration failed for setting:", args.settingId, error);
      throw error;
    }
  },
});

// Migrate all unencrypted settings to encrypted format
export const migrateAllSettingsToEncrypted = internalMutation({
  args: {},
  handler: async (ctx) => {
    const unencryptedSettings = await ctx.db
      .query("settings")
      .filter((q) => q.eq(q.field("isEncrypted"), false))
      .collect();
      
    console.log(`Migrating ${unencryptedSettings.length} unencrypted settings...`);
    
    for (const setting of unencryptedSettings) {
      try {
        await ctx.runMutation(internal.encryptionQueries.migrateSettingToEncrypted, {
          settingId: setting._id,
        });
      } catch (error) {
        console.error(`Failed to migrate setting ${setting._id}:`, error);
        // Continue with other settings
      }
    }
    
    console.log("Migration complete");
  },
});

// Test encryption/decryption functionality
export const testEncryption = internalQuery({
  args: {
    userId: v.id("users"),
    testData: v.string(),
  },
  returns: v.object({
    success: v.boolean(),
    originalData: v.string(),
    decryptedData: v.union(v.string(), v.null()),
    error: v.optional(v.string()),
  }),
  handler: async (ctx, args) => {
    const masterKey = process.env.ENCRYPTION_MASTER_KEY;
    if (!masterKey) {
      return {
        success: false,
        originalData: args.testData,
        decryptedData: null,
        error: "Encryption master key not configured",
      };
    }
    
    try {
      // Test encryption
      const encrypted = await encryptData(args.testData, masterKey, args.userId);
      
      // Test decryption
      const decrypted = await decryptData(encrypted, masterKey, args.userId);
      
      const success = decrypted === args.testData;
      
      return {
        success,
        originalData: args.testData,
        decryptedData: decrypted,
        error: success ? undefined : "Decrypted data doesn't match original",
      };
    } catch (error) {
      return {
        success: false,
        originalData: args.testData,
        decryptedData: null,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});