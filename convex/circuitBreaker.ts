// Circuit breaker implementation for external API calls

export enum CircuitState {
  CLOSED = 'CLOSED',     // Normal operation
  OPEN = 'OPEN',         // Circuit is open, failing fast
  HALF_OPEN = 'HALF_OPEN' // Testing if service is back
}

export interface CircuitBreakerConfig {
  failureThreshold: number;    // Number of failures before opening
  recoveryTimeout: number;     // Time to wait before trying again (ms)
  monitoringPeriod: number;    // Time window for failure counting (ms)
  successThreshold: number;    // Successes needed to close from half-open
}

export interface CircuitBreakerState {
  state: CircuitState;
  failureCount: number;
  lastFailureTime: number;
  successCount: number;
  lastRequestTime: number;
}

const DEFAULT_CONFIG: CircuitBreakerConfig = {
  failureThreshold: 5,
  recoveryTimeout: 60000, // 1 minute
  monitoringPeriod: 300000, // 5 minutes
  successThreshold: 3,
};

// In-memory store for circuit breaker states
const circuitStates = new Map<string, CircuitBreakerState>();

export class CircuitBreakerError extends Error {
  constructor(service: string) {
    super(`Circuit breaker is OPEN for service: ${service}`);
    this.name = 'CircuitBreakerError';
  }
}

export function getCircuitState(service: string): CircuitBreakerState {
  return circuitStates.get(service) || {
    state: CircuitState.CLOSED,
    failureCount: 0,
    lastFailureTime: 0,
    successCount: 0,
    lastRequestTime: 0,
  };
}

export function updateCircuitState(service: string, state: CircuitBreakerState): void {
  circuitStates.set(service, state);
}

export function shouldAllowRequest(
  service: string, 
  config: Partial<CircuitBreakerConfig> = {}
): boolean {
  const cfg = { ...DEFAULT_CONFIG, ...config };
  const state = getCircuitState(service);
  const now = Date.now();

  switch (state.state) {
    case CircuitState.CLOSED:
      return true;

    case CircuitState.OPEN:
      // Check if recovery timeout has passed
      if (now - state.lastFailureTime >= cfg.recoveryTimeout) {
        // Transition to half-open
        updateCircuitState(service, {
          ...state,
          state: CircuitState.HALF_OPEN,
          successCount: 0,
        });
        return true;
      }
      return false;

    case CircuitState.HALF_OPEN:
      return true;

    default:
      return true;
  }
}

export function recordSuccess(
  service: string,
  config: Partial<CircuitBreakerConfig> = {}
): void {
  const cfg = { ...DEFAULT_CONFIG, ...config };
  const state = getCircuitState(service);
  const now = Date.now();

  switch (state.state) {
    case CircuitState.CLOSED:
      // Reset failure count on success
      updateCircuitState(service, {
        ...state,
        failureCount: 0,
        lastRequestTime: now,
      });
      break;

    case CircuitState.HALF_OPEN:
      const newSuccessCount = state.successCount + 1;
      if (newSuccessCount >= cfg.successThreshold) {
        // Close the circuit
        updateCircuitState(service, {
          ...state,
          state: CircuitState.CLOSED,
          failureCount: 0,
          successCount: 0,
          lastRequestTime: now,
        });
      } else {
        updateCircuitState(service, {
          ...state,
          successCount: newSuccessCount,
          lastRequestTime: now,
        });
      }
      break;
  }
}

export function recordFailure(
  service: string,
  config: Partial<CircuitBreakerConfig> = {}
): void {
  const cfg = { ...DEFAULT_CONFIG, ...config };
  const state = getCircuitState(service);
  const now = Date.now();

  // Clean old failures outside monitoring period
  const isRecentFailure = now - state.lastFailureTime <= cfg.monitoringPeriod;
  const currentFailureCount = isRecentFailure ? state.failureCount : 0;

  const newFailureCount = currentFailureCount + 1;

  if (newFailureCount >= cfg.failureThreshold) {
    // Open the circuit
    updateCircuitState(service, {
      ...state,
      state: CircuitState.OPEN,
      failureCount: newFailureCount,
      lastFailureTime: now,
      successCount: 0,
      lastRequestTime: now,
    });
  } else {
    updateCircuitState(service, {
      ...state,
      failureCount: newFailureCount,
      lastFailureTime: now,
      lastRequestTime: now,
    });
  }
}

export async function executeWithCircuitBreaker<T>(
  service: string,
  operation: () => Promise<T>,
  config: Partial<CircuitBreakerConfig> = {}
): Promise<T> {
  if (!shouldAllowRequest(service, config)) {
    throw new CircuitBreakerError(service);
  }

  try {
    const result = await operation();
    recordSuccess(service, config);
    return result;
  } catch (error) {
    recordFailure(service, config);
    throw error;
  }
}

// Predefined configurations for different services
export const OPENROUTER_CIRCUIT_CONFIG: CircuitBreakerConfig = {
  failureThreshold: 3,
  recoveryTimeout: 30000, // 30 seconds
  monitoringPeriod: 180000, // 3 minutes
  successThreshold: 2,
};

export const LINEAR_CIRCUIT_CONFIG: CircuitBreakerConfig = {
  failureThreshold: 3,
  recoveryTimeout: 60000, // 1 minute
  monitoringPeriod: 300000, // 5 minutes
  successThreshold: 2,
};

export const TELEGRAM_CIRCUIT_CONFIG: CircuitBreakerConfig = {
  failureThreshold: 5,
  recoveryTimeout: 30000, // 30 seconds
  monitoringPeriod: 180000, // 3 minutes
  successThreshold: 3,
};

// Cleanup function to remove old circuit states
export function cleanupCircuitStates(): void {
  const now = Date.now();
  const maxAge = 3600000; // 1 hour

  for (const [service, state] of circuitStates.entries()) {
    if (now - state.lastRequestTime > maxAge) {
      circuitStates.delete(service);
    }
  }
}

// Get circuit breaker status for monitoring
export function getCircuitStatus(service: string): {
  service: string;
  state: CircuitState;
  failureCount: number;
  lastFailureTime: number;
  isHealthy: boolean;
} {
  const state = getCircuitState(service);
  return {
    service,
    state: state.state,
    failureCount: state.failureCount,
    lastFailureTime: state.lastFailureTime,
    isHealthy: state.state === CircuitState.CLOSED,
  };
}
