import { internalQuery, internalMutation } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";

// Audit logging for security events
export const logSecurityEvent = internalMutation({
  args: {
    userId: v.optional(v.id("users")),
    eventType: v.union(
      v.literal("auth_failure"),
      v.literal("rate_limit_exceeded"),
      v.literal("invalid_signature"),
      v.literal("suspicious_request"),
      v.literal("data_access"),
      v.literal("setting_change"),
      v.literal("encryption_error")
    ),
    details: v.string(),
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),
    requestUrl: v.optional(v.string()),
    severity: v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("critical")
    ),
  },
  handler: async (ctx, args) => {
    await ctx.db.insert("securityLogs", {
      userId: args.userId,
      timestamp: Date.now(),
      eventType: args.eventType,
      details: args.details,
      ipAddress: args.ipAddress,
      userAgent: args.userAgent,
      requestUrl: args.requestUrl,
      severity: args.severity,
    });
  },
});

// Check if user owns a resource (row-level security helper)
export const checkResourceOwnership = internalQuery({
  args: {
    userId: v.id("users"),
    resourceType: v.union(
      v.literal("telegramMessages"),
      v.literal("tasks"),
      v.literal("settings"),
      v.literal("webhookLogs")
    ),
    resourceId: v.string(),
  },
  returns: v.boolean(),
  handler: async (ctx, args): Promise<boolean> => {
    try {
      let resource;
      
      switch (args.resourceType) {
        case "telegramMessages":
          resource = await ctx.db.get(args.resourceId as Id<"telegramMessages">);
          break;
        case "tasks":
          resource = await ctx.db.get(args.resourceId as Id<"tasks">);
          break;
        case "settings":
          resource = await ctx.db.get(args.resourceId as Id<"settings">);
          break;
        case "webhookLogs":
          resource = await ctx.db.get(args.resourceId as Id<"webhookLogs">);
          break;
        default:
          return false;
      }
      
      return resource ? resource.userId === args.userId : false;
    } catch {
      return false;
    }
  },
});

// Get recent security events for monitoring
export const getRecentSecurityEvents = internalQuery({
  args: {
    userId: v.optional(v.id("users")),
    eventType: v.optional(v.union(
      v.literal("auth_failure"),
      v.literal("rate_limit_exceeded"),
      v.literal("invalid_signature"),
      v.literal("suspicious_request"),
      v.literal("data_access"),
      v.literal("setting_change"),
      v.literal("encryption_error")
    )),
    severity: v.optional(v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("critical")
    )),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("securityLogs").order("desc");
    
    if (args.userId) {
      query = query.filter((q) => q.eq(q.field("userId"), args.userId));
    }
    
    if (args.eventType) {
      query = query.filter((q) => q.eq(q.field("eventType"), args.eventType));
    }
    
    if (args.severity) {
      query = query.filter((q) => q.eq(q.field("severity"), args.severity));
    }
    
    return await query.take(args.limit || 50);
  },
});