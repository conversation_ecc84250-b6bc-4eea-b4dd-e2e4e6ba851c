"use node";

import { action, internalQuery } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";
import { api, internal } from "./_generated/api";

export const dbSnapshot = internalQuery({
  args: {
    userId: v.id("users"),
    limit: v.optional(v.number()),
  },
  returns: v.object({
    counts: v.object({
      telegramMessages: v.number(),
      tasks: v.number(),
      webhookLogs: v.number(),
    }),
    recent: v.object({
      messages: v.array(v.any()),
      tasks: v.array(v.any()),
      logs: v.array(v.any()),
    }),
    integrity: v.object({
      tasksWithMissingTelegramMessage: v.number(),
      tasksSyncedWithoutLinearIds: v.number(),
      duplicateTasksPerTelegramMessage: v.number(),
      stalePendingMessages: v.number(),
    }),
  }),
  handler: async (ctx, args) => {
    const limit = args.limit ?? 20;

    const messages = await ctx.db
      .query("telegramMessages")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(limit);

    const tasks = await ctx.db
      .query("tasks")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(limit);

    const logs = await ctx.db
      .query("webhookLogs")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(limit);

    // Integrity checks
    const taskMessageIds = new Map<string, number>();
    let tasksWithMissingTelegramMessage = 0;
    let tasksSyncedWithoutLinearIds = 0;
    for (const t of tasks) {
      const tmId = (t.telegramMessageId as unknown as string) || undefined;
      if (tmId) {
        taskMessageIds.set(tmId, (taskMessageIds.get(tmId) ?? 0) + 1);
      }
      if (t.status === "synced" && (!t.linearTaskId || !t.linearUrl)) {
        tasksSyncedWithoutLinearIds += 1;
      }
      // verify referenced message exists in our recent sample
      if (tmId && !messages.find((m) => (m._id as unknown as string) === tmId)) {
        // Not necessarily missing — may be outside the sample window. Count if truly missing.
        const msg = await ctx.db.get(t.telegramMessageId);
        if (!msg) tasksWithMissingTelegramMessage += 1;
      }
    }
    const duplicateTasksPerTelegramMessage = Array.from(taskMessageIds.values()).filter((c) => c > 1).length;

    const stalePendingMessages = messages.filter((m) => m.processingStatus === "pending" && Date.now() - m.date * 1000 > 5 * 60 * 1000).length;

    return {
      counts: {
        telegramMessages: messages.length,
        tasks: tasks.length,
        webhookLogs: logs.length,
      },
      recent: {
        messages,
        tasks,
        logs,
      },
      integrity: {
        tasksWithMissingTelegramMessage,
        tasksSyncedWithoutLinearIds,
        duplicateTasksPerTelegramMessage,
        stalePendingMessages,
      },
    };
  },
});

export const fullSystemDiagnostics = action({
  args: {},
  returns: v.object({
    userId: v.string(),
    convexSiteUrl: v.string(),
    httpGetOk: v.boolean(),
    settings: v.object({
      OPENROUTER_API_KEY: v.object({ exists: v.boolean(), valid: v.boolean(), message: v.string(), details: v.optional(v.string()) }),
      LINEAR_API_KEY: v.object({ exists: v.boolean(), valid: v.boolean(), message: v.string(), details: v.optional(v.string()) }),
      LINEAR_TEAM_ID: v.object({ exists: v.boolean(), valid: v.boolean(), message: v.string(), details: v.optional(v.string()) }),
      TELEGRAM_BOT_TOKEN: v.object({ exists: v.boolean(), valid: v.boolean(), message: v.string(), details: v.optional(v.string()), botUsername: v.optional(v.string()) }),
      TELEGRAM_WEBHOOK_SECRET: v.object({ exists: v.boolean(), valid: v.boolean(), message: v.string(), details: v.optional(v.string()) }),
    }),
    telegram: v.object({
      getMeOk: v.boolean(),
      bot: v.optional(v.any()),
      webhookInfo: v.optional(v.any()),
      expectedUrl: v.string(),
      secretMatches: v.optional(v.boolean()),
    }),
    db: v.any(),
  }),
  handler: async (ctx): Promise<{ userId: string; convexSiteUrl: string; httpGetOk: boolean; settings: any; telegram: any; db: any; }> => {
    const userId = await getAuthUserId(ctx);
    if (!userId) throw new Error("Not authenticated");

    // Site URL & HTTP GET
    const convexSiteUrl: string = await ctx.runQuery(api.webhooks.getConvexSiteUrl, {}) as unknown as string;
    let httpGetOk: boolean = false;
    try {
      const res = await fetch(`${convexSiteUrl}/telegram/webhook`, { method: "GET" });
      httpGetOk = res.ok;
    } catch {
      httpGetOk = false;
    }

    // Settings check
    async function decrypt(key: string): Promise<string | undefined> {
      return await ctx.runQuery(internal.encryptionQueries.decryptSetting, { userId: userId!, key });
    }

    const openrouterKey = await decrypt("OPENROUTER_API_KEY");
    const linearKey = await decrypt("LINEAR_API_KEY");
    const linearTeamId = await decrypt("LINEAR_TEAM_ID");
    const botToken = await decrypt("TELEGRAM_BOT_TOKEN");
    const webhookSecret = await decrypt("TELEGRAM_WEBHOOK_SECRET");

    // Validate OpenRouter by calling /models
    let orValid = false, orMsg = "Not configured", orDetails: string | undefined = undefined;
    if (openrouterKey) {
      try {
        const resp = await fetch("https://openrouter.ai/api/v1/models", { headers: { Authorization: `Bearer ${openrouterKey}` } });
        if (resp.ok) { orValid = true; orMsg = "Valid"; } else { orMsg = `API error ${resp.status}`; }
      } catch (e) { orMsg = "Network error"; orDetails = String(e); }
    }

    // Validate Linear by querying viewer
    let linValid = false, linMsg = "Not configured", linDetails: string | undefined = undefined;
    if (linearKey) {
      try {
        const resp = await fetch("https://api.linear.app/graphql", {
          method: "POST",
          headers: { Authorization: linearKey, "Content-Type": "application/json" },
          body: JSON.stringify({ query: "query { viewer { id name email } }" }),
        });
        const data = await resp.json();
        if (resp.ok && !data.errors) { linValid = true; linMsg = "Valid"; linDetails = `Connected as ${data.data.viewer?.name || data.data.viewer?.email}`; }
        else { linMsg = data.errors?.[0]?.message || `HTTP ${resp.status}`; }
      } catch (e) { linMsg = "Network error"; linDetails = String(e); }
    }

    // Validate Telegram token via getMe and webhook info
    let tgGetMeOk = false; let botInfo: any = undefined; let webhookInfo: any = undefined; let secretMatches: boolean | undefined = undefined;
    const expectedUrl = `${convexSiteUrl}/telegram/webhook`;
    if (botToken) {
      try {
        const me = await fetch(`https://api.telegram.org/bot${botToken}/getMe`);
        const meData = await me.json();
        tgGetMeOk = !!meData?.ok;
        if (tgGetMeOk) botInfo = meData.result;
      } catch {}
      try {
        const wi = await fetch(`https://api.telegram.org/bot${botToken}/getWebhookInfo`);
        const wiData = await wi.json();
        if (wiData?.ok) {
          webhookInfo = wiData.result;
          if (webhookSecret && webhookInfo?.url) {
            // We can't read stored secret from Telegram; just compare URLs
            secretMatches = webhookInfo.url === expectedUrl;
          }
        }
      } catch {}
    }

    // Secret validation
    let secretValid = false; let secretMsg = "Not configured"; let secretDetails: string | undefined = undefined;
    if (webhookSecret) {
      const trimmed = webhookSecret.trim();
      const allowed = /^[A-Za-z0-9_-]+$/.test(trimmed);
      if (!allowed) { secretMsg = "Invalid characters"; secretDetails = "Allowed: A–Z, a–z, 0–9, _ and -"; }
      else if (trimmed.length < 32) { secretMsg = "Too short"; secretDetails = "Min 32 characters"; }
      else if (trimmed.length > 256) { secretMsg = "Too long"; secretDetails = "Max 256 characters"; }
      else { secretValid = true; secretMsg = "Valid"; }
    }

    // DB snapshot & integrity
    const snapshot: any = await ctx.runQuery(internal.diagnostics.dbSnapshot, { userId, limit: 50 });

    return {
      userId,
      convexSiteUrl,
      httpGetOk,
      settings: {
        OPENROUTER_API_KEY: { exists: !!openrouterKey, valid: orValid, message: orMsg, details: orDetails },
        LINEAR_API_KEY: { exists: !!linearKey, valid: linValid, message: linMsg, details: linDetails },
        LINEAR_TEAM_ID: { exists: !!linearTeamId, valid: !!linearTeamId, message: linearTeamId ? "Present" : "Missing" },
        TELEGRAM_BOT_TOKEN: { exists: !!botToken, valid: tgGetMeOk, message: tgGetMeOk ? "Valid" : (botToken ? "Invalid or network error" : "Missing"), botUsername: botInfo?.username },
        TELEGRAM_WEBHOOK_SECRET: { exists: !!webhookSecret, valid: secretValid, message: secretMsg, details: secretDetails },
      },
      telegram: {
        getMeOk: tgGetMeOk,
        bot: botInfo,
        webhookInfo,
        expectedUrl,
        secretMatches,
      },
      db: snapshot,
    };
  },
});
