"use node";

import { action } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";
import { internal } from "./_generated/api";

// Security actions
export const generateWebhookSecret = action({
  args: {},
  returns: v.string(),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Generate a Telegram-compliant webhook secret (only A-Z, a-z, 0-9, _, -)
    const allowedChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_-';
    const randomBytes = new Uint8Array(32);
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      crypto.getRandomValues(randomBytes);
    } else {
      // Fallback for environments without crypto
      for (let i = 0; i < 32; i++) {
        randomBytes[i] = Math.floor(Math.random() * 256);
      }
    }
    const secret = Array.from(randomBytes, byte => {
      const index = byte % allowedChars.length;
      return allowedChars[index];
    }).join('');

    console.log("🔑 Generated webhook secret:", secret);
    console.log("🔍 Secret length:", secret.length);
    console.log("🔍 Secret validation:", /^[A-Za-z0-9_-]+$/.test(secret));

    // Store the webhook secret
    await ctx.runMutation(internal.encryptionQueries.encryptSetting, {
      userId,
      key: "TELEGRAM_WEBHOOK_SECRET",
      value: secret,
    });

    console.log("💾 Webhook secret stored successfully");

    // Log the security event
    await ctx.runMutation(internal.securityQueries.logSecurityEvent, {
      userId,
      eventType: "setting_change",
      details: "Generated new Telegram webhook secret",
      severity: "medium",
    });

    return secret;
  },
});

// Generate and return a webhook secret for immediate use (doesn't store it)
export const generateWebhookSecretPreview = action({
  args: {},
  returns: v.string(),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Generate a Telegram-compliant webhook secret (preview only, only A-Z, a-z, 0-9, _, -)
    const allowedChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_-';
    const randomBytes = new Uint8Array(32);
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      crypto.getRandomValues(randomBytes);
    } else {
      // Fallback for environments without crypto
      for (let i = 0; i < 32; i++) {
        randomBytes[i] = Math.floor(Math.random() * 256);
      }
    }
    const secret = Array.from(randomBytes, byte => {
      const index = byte % allowedChars.length;
      return allowedChars[index];
    }).join('');

    return secret;
  },
});

// Test webhook secret validation
export const testWebhookSecret = action({
  args: {},
  returns: v.object({
    success: v.boolean(),
    secret: v.optional(v.string()),
    isValid: v.boolean(),
    length: v.number(),
    error: v.optional(v.string()),
  }),
  handler: async (ctx): Promise<{
    success: boolean;
    secret?: string;
    isValid: boolean;
    length: number;
    error?: string;
  }> => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    try {
      const secret: string | null = await ctx.runQuery(internal.settings.getSetting, {
        key: "TELEGRAM_WEBHOOK_SECRET",
        userId,
      });

      if (!secret) {
        return {
          success: false,
          isValid: false,
          length: 0,
          error: "No webhook secret found",
        };
      }

      const isValid = /^[A-Za-z0-9_-]+$/.test(secret);

      return {
        success: true,
        secret: secret.substring(0, 8) + "...", // Only show first 8 chars
        isValid,
        length: secret.length,
        error: isValid ? undefined : "Secret contains invalid characters",
      };
    } catch (error) {
      return {
        success: false,
        isValid: false,
        length: 0,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  },
});

export const testEncryption = action({
  args: {
    testData: v.optional(v.string()),
  },
  returns: v.object({
    success: v.boolean(),
    originalData: v.string(),
    decryptedData: v.union(v.string(), v.null()),
    error: v.optional(v.string()),
    encryptionEnabled: v.boolean(),
  }),
  handler: async (ctx, args): Promise<{
    success: boolean;
    originalData: string;
    decryptedData: string | null;
    error?: string;
    encryptionEnabled: boolean;
  }> => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const testData = args.testData || `test_encryption_${Date.now()}`;
    const encryptionEnabled = process.env.ENCRYPTION_MASTER_KEY ? true : false;

    if (!encryptionEnabled) {
      return {
        success: false,
        originalData: testData,
        decryptedData: null,
        error: "Encryption not enabled - ENCRYPTION_MASTER_KEY not set",
        encryptionEnabled: false,
      };
    }

    try {
      const result: {
        success: boolean;
        originalData: string;
        decryptedData: string | null;
        error?: string;
      } = await ctx.runQuery(internal.encryptionQueries.testEncryption, {
        userId,
        testData,
      });

      // Log the test
      await ctx.runMutation(internal.securityQueries.logSecurityEvent, {
        userId,
        eventType: "data_access",
        details: `Encryption test ${result.success ? 'passed' : 'failed'}`,
        severity: result.success ? "low" : "high",
      });

      return {
        ...result,
        encryptionEnabled: true,
      };
    } catch (error) {
      await ctx.runMutation(internal.securityQueries.logSecurityEvent, {
        userId,
        eventType: "encryption_error",
        details: `Encryption test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: "critical",
      });

      return {
        success: false,
        originalData: testData,
        decryptedData: null,
        error: error instanceof Error ? error.message : "Unknown error",
        encryptionEnabled: true,
      };
    }
  },
});

export const migrateUserSettings = action({
  args: {},
  returns: v.object({
    success: v.boolean(),
    message: v.string(),
  }),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    if (!process.env.ENCRYPTION_MASTER_KEY) {
      throw new Error("Encryption not enabled - ENCRYPTION_MASTER_KEY not set");
    }

    try {
      // Run the global migration function instead of accessing ctx.db directly
      await ctx.runMutation(internal.encryptionQueries.migrateAllSettingsToEncrypted, {});

      // Log the migration
      await ctx.runMutation(internal.securityQueries.logSecurityEvent, {
        userId,
        eventType: "setting_change",
        details: "User requested settings migration",
        severity: "medium",
      });

      return {
        success: true,
        message: "Settings migration completed successfully",
      };
    } catch (error) {
      await ctx.runMutation(internal.securityQueries.logSecurityEvent, {
        userId,
        eventType: "encryption_error",
        details: `Settings migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: "critical",
      });

      return {
        success: false,
        message: `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  },
});

// Utility to clear rate limits (admin function)
export const clearRateLimits = action({
  args: {
    ipAddress: v.optional(v.string()),
  },
  returns: v.object({
    success: v.boolean(),
    message: v.string(),
  }),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // This would need to be implemented in the security module
    // For now, just log the action
    await ctx.runMutation(internal.securityQueries.logSecurityEvent, {
      userId,
      eventType: "data_access",
      details: `Rate limit clear requested${args.ipAddress ? ` for IP: ${args.ipAddress}` : ' (all)'}`,
      severity: "medium",
    });

    return {
      success: true,
      message: "Rate limits cleared successfully",
    };
  },
});