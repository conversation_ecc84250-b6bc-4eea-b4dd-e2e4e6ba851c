"use node";

import { z } from "zod";

// Telegram webhook payload schemas
export const TelegramUserSchema = z.object({
  id: z.number(),
  is_bot: z.boolean(),
  first_name: z.string(),
  last_name: z.string().optional(),
  username: z.string().optional(),
  language_code: z.string().optional(),
});

export const TelegramChatSchema = z.object({
  id: z.number(),
  type: z.enum(["private", "group", "supergroup", "channel"]),
  title: z.string().optional(),
  username: z.string().optional(),
  first_name: z.string().optional(),
  last_name: z.string().optional(),
});

export const TelegramMessageSchema: z.ZodType<any> = z.object({
  message_id: z.number(),
  from: TelegramUserSchema.optional(),
  date: z.number(),
  chat: TelegramChatSchema,
  text: z.string().optional(),
  caption: z.string().optional(),
  forward_from: TelegramUserSchema.optional(),
  forward_from_chat: TelegramChatSchema.optional(),
  forward_from_message_id: z.number().optional(),
  forward_date: z.number().optional(),
  reply_to_message: z.lazy((): z.ZodType<any> => TelegramMessageSchema).optional(),
});

export const TelegramUpdateSchema = z.object({
  update_id: z.number(),
  message: TelegramMessageSchema.optional(),
  edited_message: TelegramMessageSchema.optional(),
  channel_post: TelegramMessageSchema.optional(),
  edited_channel_post: TelegramMessageSchema.optional(),
});

// API key validation schemas
export const OpenRouterApiKeySchema = z.string()
  .min(1, "API key is required")
  .regex(/^sk-or-v1-[a-f0-9]{64}$/, "Invalid OpenRouter API key format");

export const LinearApiKeySchema = z.string()
  .min(1, "API key is required")
  .regex(/^lin_api_[a-zA-Z0-9]{40}$/, "Invalid Linear API key format");

export const TelegramBotTokenSchema = z.string()
  .min(1, "Bot token is required")
  .regex(/^[0-9]{8,10}:[a-zA-Z0-9_-]{35}$/, "Invalid Telegram bot token format");

// Security validation
export const WebhookSecretSchema = z.string()
  .min(32, "Webhook secret must be at least 32 characters")
  .max(256, "Webhook secret too long");

// Input sanitization helpers
export function sanitizeText(text: string): string {
  return text
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/javascript:/gi, '') // Remove javascript: URIs
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .replace(/[\x00-\x1F\x7F-\x9F]/g, '') // Remove control characters
    .trim()
    .slice(0, 10000); // Limit length
}

export function sanitizeUrl(url: string): string {
  try {
    const parsed = new URL(url);
    // Only allow HTTP/HTTPS protocols
    if (!['http:', 'https:'].includes(parsed.protocol)) {
      throw new Error('Invalid protocol');
    }
    return parsed.toString();
  } catch {
    throw new Error('Invalid URL format');
  }
}

// Telegram webhook signature validation
export async function validateTelegramSignature(
  payload: string,
  signature: string,
  secret: string
): Promise<boolean> {
  if (!signature || !secret) {
    return false;
  }

  try {
    // Create HMAC SHA-256 hash
    const encoder = new TextEncoder();
    const keyData = encoder.encode(secret);
    const messageData = encoder.encode(payload);
    
    const key = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    );
    
    const signatureBuffer = await crypto.subtle.sign('HMAC', key, messageData);
    const expectedSignature = Array.from(new Uint8Array(signatureBuffer))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
    
    // Compare signatures in constant time to prevent timing attacks
    if (signature.length !== expectedSignature.length) {
      return false;
    }
    
    let result = 0;
    for (let i = 0; i < signature.length; i++) {
      result |= signature.charCodeAt(i) ^ expectedSignature.charCodeAt(i);
    }
    
    return result === 0;
  } catch {
    return false;
  }
}

// Rate limiting validation
export const RateLimitSchema = z.object({
  maxRequests: z.number().min(1).max(10000),
  windowMs: z.number().min(1000).max(3600000), // 1 second to 1 hour
  skipSuccessfulRequests: z.boolean().optional(),
  skipFailedRequests: z.boolean().optional(),
});

// Environment variable validation
export const SecurityConfigSchema = z.object({
  ENCRYPTION_MASTER_KEY: z.string()
    .min(32, "Encryption master key must be at least 32 characters")
    .regex(/^[A-Za-z0-9+/]{32,}={0,2}$/, "Invalid base64 encryption key format"),
  WEBHOOK_SECRET: z.string()
    .min(32, "Webhook secret must be at least 32 characters"),
  MAX_REQUEST_SIZE: z.string()
    .regex(/^\d+$/, "Must be a number")
    .transform(Number)
    .refine(n => n > 0 && n <= 50 * 1024 * 1024, "Request size must be between 1 byte and 50MB"),
});

// Database validation schemas
export const UserIdSchema = z.string().regex(/^[a-zA-Z0-9]{16}$/, "Invalid user ID format");

export const SettingKeySchema = z.string()
  .min(1, "Setting key is required")
  .max(100, "Setting key too long")
  .regex(/^[A-Z_][A-Z0-9_]*$/, "Setting key must use UPPER_CASE format");

export const SettingValueSchema = z.string()
  .max(8192, "Setting value too long"); // Convex limit is 8KB

// Task validation schemas
export const TaskPrioritySchema = z.enum(["low", "medium", "high", "urgent"]);

export const TaskStatusSchema = z.enum(["created", "syncing", "synced", "failed"]);

export const TaskTitleSchema = z.string()
  .min(1, "Task title is required")
  .max(200, "Task title too long")
  .transform(sanitizeText);

export const TaskDescriptionSchema = z.string()
  .min(1, "Task description is required")
  .max(5000, "Task description too long")
  .transform(sanitizeText);

// Linear webhook validation
export const LinearWebhookSchema = z.object({
  action: z.string(),
  actor: z.object({
    id: z.string(),
    name: z.string(),
  }),
  data: z.record(z.unknown()),
  url: z.string().url(),
  type: z.string(),
  organizationId: z.string(),
  webhookTimestamp: z.number(),
  webhookId: z.string(),
});

// Content Security Policy helpers
export const CSP_DIRECTIVES = {
  'default-src': ["'self'"],
  'script-src': ["'self'", "'unsafe-inline'"],
  'style-src': ["'self'", "'unsafe-inline'"],
  'img-src': ["'self'", "data:", "https:"],
  'font-src': ["'self'"],
  'connect-src': ["'self'", "https://fastidious-mosquito-539.convex.cloud"],
  'form-action': ["'self'"],
  'frame-ancestors': ["'none'"],
  'object-src': ["'none'"],
  'base-uri': ["'self'"],
} as const;

export function buildCSPHeader(): string {
  return Object.entries(CSP_DIRECTIVES)
    .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
    .join('; ');
}

// IP address validation for rate limiting
export function extractClientIP(request: Request): string {
  // Check various headers for the real client IP
  const forwardedFor = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (cfConnectingIP) return cfConnectingIP;
  if (realIP) return realIP;
  if (forwardedFor) return forwardedFor.split(',')[0].trim();
  
  return 'unknown';
}

// Request size validation
export function validateRequestSize(contentLength: string | null, maxSize: number = 1024 * 1024): boolean {
  if (!contentLength) return true; // Let through if no content-length
  const size = parseInt(contentLength, 10);
  return !isNaN(size) && size <= maxSize;
}

// Secure random token generation
export function generateSecureToken(length: number = 32): string {
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

// Time-based validation for preventing replay attacks
export function validateTimestamp(timestamp: number, maxAgeSeconds: number = 300): boolean {
  const now = Math.floor(Date.now() / 1000);
  const diff = Math.abs(now - timestamp);
  return diff <= maxAgeSeconds;
}

// Extract webhook secret from Request header or query string
export function getWebhookSecretFromRequest(request: Request): { secret: string | null; source: 'header' | 'query' | null } {
  // Header preferred by Telegram: X-Telegram-Bot-Api-Secret-Token
  const headerSecret = request.headers.get('X-Telegram-Bot-Api-Secret-Token');
  if (headerSecret && headerSecret.trim().length > 0) {
    return { secret: headerSecret, source: 'header' };
  }

  // Backwards-compatible query param (?secret=...)
  try {
    const url = new URL(request.url);
    const qp = url.searchParams.get('secret');
    if (qp && qp.trim().length > 0) {
      return { secret: qp, source: 'query' };
    }
  } catch {
    // ignore URL parse errors
  }

  return { secret: null, source: null };
}
