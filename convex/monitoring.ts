import { query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { getCircuitStatus } from "./circuitBreaker";
import { api } from "./_generated/api";

// Get circuit breaker status for monitoring
export const getCircuitBreakerStatus = query({
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Get status for all monitored services
    const services = ['openrouter', 'linear', 'telegram'];
    const statuses = services.map(service => getCircuitStatus(service));

    return {
      services: statuses,
      timestamp: Date.now(),
      overallHealth: statuses.every(s => s.isHealthy),
    };
  },
});

// Get system health overview
export const getSystemHealth = query({
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Get circuit breaker status directly
    const services = ['openrouter', 'linear', 'telegram'];
    const circuitStatuses = services.map(service => getCircuitStatus(service));
    const circuitStatus = {
      services: circuitStatuses,
      timestamp: Date.now(),
      overallHealth: circuitStatuses.every(s => s.isHealthy),
    };
    
    // Get recent error counts from security logs
    const recentErrors = await ctx.db
      .query("securityLogs")
      .withIndex("by_timestamp", (q) => 
        q.gte("timestamp", Date.now() - 3600000) // Last hour
      )
      .filter((q) => q.eq(q.field("severity"), "high"))
      .collect();

    // Get recent webhook stats
    const recentWebhooks = await ctx.db
      .query("webhookLogs")
      .withIndex("by_timestamp", (q) => 
        q.gte("timestamp", Date.now() - 3600000) // Last hour
      )
      .collect();

    const webhookSuccessRate = recentWebhooks.length > 0 ? 
      (recentWebhooks.filter(w => w.status === "success").length / recentWebhooks.length) * 100 : 
      100;

    return {
      circuitBreakers: circuitStatus,
      errorCount: recentErrors.length,
      webhookSuccessRate: Math.round(webhookSuccessRate),
      lastUpdated: Date.now(),
      status: circuitStatus.overallHealth && webhookSuccessRate > 90 ? 'healthy' : 'degraded',
    };
  },
});

// Get performance metrics
export const getPerformanceMetrics = query({
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const now = Date.now();
    const oneHourAgo = now - 3600000;
    const oneDayAgo = now - 86400000;

    // Get message processing times (approximate)
    const recentMessages = await ctx.db
      .query("telegramMessages")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .filter((q) => q.gte(q.field("date"), oneHourAgo / 1000))
      .collect();

    const processingStats = {
      total: recentMessages.length,
      completed: recentMessages.filter(m => m.processingStatus === "completed").length,
      failed: recentMessages.filter(m => m.processingStatus === "failed").length,
      pending: recentMessages.filter(m => m.processingStatus === "pending").length,
      processing: recentMessages.filter(m => m.processingStatus === "processing").length,
    };

    // Get task creation success rate
    const recentTasks = await ctx.db
      .query("tasks")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .filter((q) => q.gte(q.field("_creationTime"), oneDayAgo))
      .collect();

    const taskStats = {
      total: recentTasks.length,
      synced: recentTasks.filter(t => t.status === "synced").length,
      partialSync: recentTasks.filter(t => t.status === "partial_sync").length,
      failed: recentTasks.filter(t => t.status === "failed").length,
    };

    const taskSuccessRate = recentTasks.length > 0 ? 
      ((taskStats.synced + taskStats.partialSync) / taskStats.total) * 100 : 
      100;

    return {
      messageProcessing: processingStats,
      taskCreation: taskStats,
      taskSuccessRate: Math.round(taskSuccessRate),
      timeRange: {
        messages: "Last hour",
        tasks: "Last 24 hours",
      },
      timestamp: now,
    };
  },
});
