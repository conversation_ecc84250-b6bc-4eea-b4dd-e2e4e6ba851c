import { mutation, query, internalMutation, internalQuery } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

export const createTask = internalMutation({
  args: {
    userId: v.id("users"),
    telegramMessageId: v.id("telegramMessages"),
    title: v.string(),
    description: v.string(),
    priority: v.union(
      v.literal("low"),
      v.literal("medium"),
      v.literal("high"),
      v.literal("urgent")
    ),
    subtasks: v.array(v.object({
      title: v.string(),
      description: v.string(),
    })),
  },
  handler: async (ctx, args) => {
    const subtasksWithStatus = args.subtasks.map(subtask => ({
      ...subtask,
      status: "created" as const,
    }));

    return await ctx.db.insert("tasks", {
      userId: args.userId,
      telegramMessageId: args.telegramMessageId,
      title: args.title,
      description: args.description,
      priority: args.priority,
      subtasks: subtasksWithStatus,
      status: "created",
    });
  },
});

export const getAllTasks = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const tasks = await ctx.db
      .query("tasks")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .order("desc")
      .take(100);

    // Get associated telegram messages
    const tasksWithMessages = await Promise.all(
      tasks.map(async (task) => {
        const message = await ctx.db.get(task.telegramMessageId);
        return {
          ...task,
          telegramMessage: message,
        };
      })
    );

    return tasksWithMessages;
  },
});

export const getTaskStats = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return {
        total: 0,
        created: 0,
        syncing: 0,
        synced: 0,
        failed: 0,
        byPriority: {
          urgent: 0,
          high: 0,
          medium: 0,
          low: 0,
        }
      };
    }

    const tasks = await ctx.db
      .query("tasks")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();
    
    const stats = {
      total: tasks.length,
      created: tasks.filter(t => t.status === "created").length,
      syncing: tasks.filter(t => t.status === "syncing").length,
      synced: tasks.filter(t => t.status === "synced").length,
      failed: tasks.filter(t => t.status === "failed").length,
      byPriority: {
        urgent: tasks.filter(t => t.priority === "urgent").length,
        high: tasks.filter(t => t.priority === "high").length,
        medium: tasks.filter(t => t.priority === "medium").length,
        low: tasks.filter(t => t.priority === "low").length,
      }
    };

    return stats;
  },
});

export const updateTaskStatus = internalMutation({
  args: {
    taskId: v.id("tasks"),
    status: v.union(
      v.literal("created"),
      v.literal("syncing"),
      v.literal("synced"),
      v.literal("partial_sync"),
      v.literal("failed")
    ),
    linearTaskId: v.optional(v.string()),
    linearUrl: v.optional(v.string()),
    errorMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const updateData: any = {
      status: args.status,
    };

    if (args.linearTaskId) updateData.linearTaskId = args.linearTaskId;
    if (args.linearUrl) updateData.linearUrl = args.linearUrl;
    if (args.errorMessage) updateData.errorMessage = args.errorMessage;

    await ctx.db.patch(args.taskId, updateData);
  },
});

export const getTask = internalQuery({
  args: {
    taskId: v.id("tasks"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.taskId);
  },
});
