/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as admin from "../admin.js";
import type * as adminQueries from "../adminQueries.js";
import type * as ai from "../ai.js";
import type * as auth from "../auth.js";
import type * as circuitBreaker from "../circuitBreaker.js";
import type * as diagnostics from "../diagnostics.js";
import type * as encryptionQueries from "../encryptionQueries.js";
import type * as http from "../http.js";
import type * as linear from "../linear.js";
import type * as monitoring from "../monitoring.js";
import type * as retryUtils from "../retryUtils.js";
import type * as security from "../security.js";
import type * as securityQueries from "../securityQueries.js";
import type * as settings from "../settings.js";
import type * as tasks from "../tasks.js";
import type * as telegram from "../telegram.js";
import type * as validation from "../validation.js";
import type * as webhooks from "../webhooks.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  admin: typeof admin;
  adminQueries: typeof adminQueries;
  ai: typeof ai;
  auth: typeof auth;
  circuitBreaker: typeof circuitBreaker;
  diagnostics: typeof diagnostics;
  encryptionQueries: typeof encryptionQueries;
  http: typeof http;
  linear: typeof linear;
  monitoring: typeof monitoring;
  retryUtils: typeof retryUtils;
  security: typeof security;
  securityQueries: typeof securityQueries;
  settings: typeof settings;
  tasks: typeof tasks;
  telegram: typeof telegram;
  validation: typeof validation;
  webhooks: typeof webhooks;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
