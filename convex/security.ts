"use node";

// Rate limiting implementation
interface RateLimitEntry {
  count: number;
  windowStart: number;
}

const rateLimitStore = new Map<string, RateLimitEntry>();

export function checkRateLimit(
  key: string,
  maxRequests: number = 200, // Increased from 100 to 200
  windowMs: number = 60000 // 1 minute
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const windowStart = Math.floor(now / windowMs) * windowMs;
  
  const entry = rateLimitStore.get(key);
  
  if (!entry || entry.windowStart < windowStart) {
    // New window or no entry
    rateLimitStore.set(key, { count: 1, windowStart });
    return {
      allowed: true,
      remaining: maxRequests - 1,
      resetTime: windowStart + windowMs,
    };
  }
  
  if (entry.count >= maxRequests) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: windowStart + windowMs,
    };
  }
  
  entry.count++;
  return {
    allowed: true,
    remaining: maxRequests - entry.count,
    resetTime: windowStart + windowMs,
  };
}

// User-specific rate limiting with different tiers
export function checkUserRateLimit(
  userId: string,
  action: 'webhook' | 'ai_processing' | 'linear_sync',
  userTier: 'free' | 'pro' = 'free'
): { allowed: boolean; remaining: number; resetTime: number } {
  const limits = {
    free: {
      webhook: { max: 100, window: 60000 }, // 100 per minute
      ai_processing: { max: 50, window: 60000 }, // 50 per minute
      linear_sync: { max: 30, window: 60000 }, // 30 per minute
    },
    pro: {
      webhook: { max: 500, window: 60000 }, // 500 per minute
      ai_processing: { max: 200, window: 60000 }, // 200 per minute
      linear_sync: { max: 100, window: 60000 }, // 100 per minute
    }
  };

  const limit = limits[userTier][action];
  const key = `user:${userId}:${action}`;

  return checkRateLimit(key, limit.max, limit.window);
}

// Clean up old rate limit entries
export function cleanupRateLimit(): void {
  const now = Date.now();
  const cutoff = now - 3600000; // Remove entries older than 1 hour

  for (const [key, entry] of rateLimitStore.entries()) {
    if (entry.windowStart < cutoff) {
      rateLimitStore.delete(key);
    }
  }
}

// HMAC signature verification
export async function verifyHMACSignature(
  payload: string,
  signature: string,
  secret: string
): Promise<boolean> {
  try {
    const encoder = new TextEncoder();
    const keyData = encoder.encode(secret);
    const messageData = encoder.encode(payload);
    
    const key = await crypto.subtle.importKey(
      'raw',
      keyData,
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    );
    
    const signatureBuffer = await crypto.subtle.sign('HMAC', key, messageData);
    const expectedSignature = Buffer.from(signatureBuffer).toString('hex');
    
    // Remove any prefix from the signature (like "sha256=")
    const cleanSignature = signature.replace(/^sha256=/, '');
    
    // Constant-time comparison to prevent timing attacks
    if (cleanSignature.length !== expectedSignature.length) {
      return false;
    }
    
    let result = 0;
    for (let i = 0; i < cleanSignature.length; i++) {
      result |= cleanSignature.charCodeAt(i) ^ expectedSignature.charCodeAt(i);
    }
    
    return result === 0;
  } catch (error) {
    console.error('HMAC verification failed:', error);
    return false;
  }
}

// Check for suspicious patterns
export async function detectSuspiciousActivity(
  ipAddress: string,
  userAgent: string,
  endpoint: string
): Promise<{ suspicious: boolean; reason?: string }> {
  // Check for common attack patterns
  const suspiciousPatterns = [
    /\.\.\//g, // Directory traversal
    /<script/gi, // XSS attempts
    /union\s+select/gi, // SQL injection
    /javascript:/gi, // Javascript URIs
    /data:text\/html/gi, // Data URIs
    /%3Cscript/gi, // URL encoded script tags
    /eval\s*\(/gi, // Code injection
    /system\s*\(/gi, // System calls
  ];
  
  const combinedInput = `${userAgent} ${endpoint}`;
  
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(combinedInput)) {
      return {
        suspicious: true,
        reason: `Suspicious pattern detected: ${pattern.source}`,
      };
    }
  }
  
  // Check for suspicious user agents
  const suspiciousUserAgents = [
    /nikto/i,
    /nmap/i,
    /sqlmap/i,
    /burp/i,
    /masscan/i,
    /zap/i,
    /gobuster/i,
    /dirb/i,
  ];
  
  for (const pattern of suspiciousUserAgents) {
    if (pattern.test(userAgent)) {
      return {
        suspicious: true,
        reason: `Suspicious user agent: ${userAgent}`,
      };
    }
  }
  
  return { suspicious: false };
}

// Generate secure webhook secret (Telegram-compliant: only A-Z, a-z, 0-9, _, -)
export function generateWebhookSecret(): string {
  const allowedChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_-';
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  
  return Array.from(array, byte => {
    // Use modulo to map byte values to allowed character indices
    const index = byte % allowedChars.length;
    return allowedChars[index];
  }).join('');
}

// Validate webhook timestamp (prevent replay attacks)
export function validateWebhookTimestamp(
  timestamp: number,
  maxAgeSeconds: number = 300
): boolean {
  const now = Math.floor(Date.now() / 1000);
  const age = Math.abs(now - timestamp);
  return age <= maxAgeSeconds;
}

// Content Security Policy builder
export function buildSecurityHeaders(): Record<string, string> {
  return {
    // Content Security Policy
    'Content-Security-Policy': [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "font-src 'self'",
      "connect-src 'self' https://fastidious-mosquito-539.convex.cloud https://api.openrouter.ai https://api.linear.app https://api.telegram.org",
      "form-action 'self'",
      "frame-ancestors 'none'",
      "object-src 'none'",
      "base-uri 'self'",
    ].join('; '),
    
    // Strict Transport Security
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
    
    // Prevent MIME type sniffing
    'X-Content-Type-Options': 'nosniff',
    
    // XSS Protection
    'X-XSS-Protection': '1; mode=block',
    
    // Frame options
    'X-Frame-Options': 'DENY',
    
    // Referrer Policy
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    
    // Permissions Policy
    'Permissions-Policy': [
      'geolocation=()',
      'microphone=()',
      'camera=()',
      'fullscreen=(self)',
      'payment=()',
    ].join(', '),
  };
}

// Input sanitization utilities
export function sanitizeForLog(input: string): string {
  return input
    .replace(/[\x00-\x1F\x7F-\x9F]/g, '') // Remove control characters
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '[SCRIPT_REMOVED]')
    .slice(0, 1000); // Limit length for logs
}

// Check if request is from allowed origin
export function validateOrigin(origin: string | null, allowedOrigins: string[]): boolean {
  if (!origin) return false;
  return allowedOrigins.includes(origin);
}

// Extract and validate Bearer token
export function extractBearerToken(authHeader: string | null): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.slice(7);
}