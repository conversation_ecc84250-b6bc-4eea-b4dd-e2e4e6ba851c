import { query, internalQuery, internalMutation, internalAction } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Id } from "./_generated/dataModel";

// Wrapper action that can be scheduled from webhook
export const processIncomingMessageAsync = internalAction({
  args: {
    messageId: v.number(),
    text: v.string(),
    date: v.number(),
    chatId: v.number(),
    botToken: v.string(),
  },
  handler: async (ctx, args) => {
    // Call the existing mutation
    await ctx.runMutation(internal.telegram.processIncomingMessage, args);
  },
});

export const processIncomingMessage = internalMutation({
  args: {
    messageId: v.number(),
    text: v.string(),
    date: v.number(),
    chatId: v.number(),
    botToken: v.string(), // We'll use this to identify the user
  },
  handler: async (ctx, args): Promise<Id<"telegramMessages">> => {
    // Find user by their bot token
    const userId: Id<"users"> | null = await ctx.runQuery(internal.telegram.findUserByBotToken, {
      botToken: args.botToken,
    });

    if (!userId) {
      throw new Error("Bot token not found - user not configured");
    }

    // Check if message already exists using messageId + chatId for proper deduplication
    const existing = await ctx.db
      .query("telegramMessages")
      .withIndex("by_message_and_chat", (q) =>
        q.eq("messageId", args.messageId).eq("chatId", args.chatId)
      )
      .first();

    if (existing) {
      console.log(`🔄 Duplicate message detected: messageId=${args.messageId}, chatId=${args.chatId}`);
      return existing._id;
    }

    // Insert new message
    const messageId = await ctx.db.insert("telegramMessages", {
      userId,
      messageId: args.messageId,
      text: args.text,
      date: args.date,
      chatId: args.chatId,
      processed: false,
      processingStatus: "pending",
    });

    // Schedule AI processing
    await ctx.scheduler.runAfter(0, internal.ai.processMessageWithAI, {
      messageId,
      userId,
    });

    return messageId;
  },
});

// Send a Telegram message back to the chat
export const sendMessage = internalAction({
  args: {
    botToken: v.string(),
    chatId: v.number(),
    text: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const resp = await fetch(`https://api.telegram.org/bot${args.botToken}/sendMessage`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          chat_id: args.chatId,
          text: args.text,
          disable_web_page_preview: true,
        }),
      });
      if (!resp.ok) {
        let desc = "";
        try {
          const data = await resp.json();
          desc = data?.description || "";
        } catch {}
        throw new Error(`Telegram sendMessage failed (${resp.status}) ${desc}`);
      }
    } catch (err) {
      // Swallow errors to avoid crashing the workflow; consider logging later
      console.log("telegram.sendMessage error", err);
    }
  },
});

export const findUserByBotToken = internalQuery({
  args: {
    botToken: v.string(),
  },
  handler: async (ctx, args): Promise<Id<"users"> | null> => {
    // Get all users and check their bot tokens
    const users = await ctx.db.query("users").collect();
    
    for (const user of users) {
      const userBotToken = await ctx.runQuery(internal.settings.getSetting, {
        key: "TELEGRAM_BOT_TOKEN",
        userId: user._id,
      });
      
      if (userBotToken === args.botToken) {
        return user._id;
      }
    }
    
    return null;
  },
});

export const findUserByWebhookSecret = internalQuery({
  args: {
    webhookSecret: v.string(),
  },
  returns: v.union(v.id("users"), v.null()),
  handler: async (ctx, args): Promise<Id<"users"> | null> => {
    // Get all users and check their webhook secrets
    const users = await ctx.db.query("users").collect();

    for (const user of users) {
      const userWebhookSecret = await ctx.runQuery(internal.settings.getSetting, {
        key: "TELEGRAM_WEBHOOK_SECRET",
        userId: user._id,
      });

      if (userWebhookSecret === args.webhookSecret) {
        return user._id;
      }
    }

    return null;
  },
});

// Find user by Telegram secret token (proper webhook authentication)
export const findUserByTelegramSecret = internalQuery({
  args: {
    telegramSecretToken: v.string(),
  },
  returns: v.union(v.id("users"), v.null()),
  handler: async (ctx, args): Promise<Id<"users"> | null> => {
    // Get all users and check their stored webhook secrets
    const users = await ctx.db.query("users").collect();

    for (const user of users) {
      const userWebhookSecret = await ctx.runQuery(internal.settings.getSetting, {
        key: "TELEGRAM_WEBHOOK_SECRET",
        userId: user._id,
      });

      // The secret token should match the webhook secret they configured
      if (userWebhookSecret === args.telegramSecretToken) {
        return user._id;
      }
    }

    return null;
  },
});

export const getRecentMessages = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    return await ctx.db
      .query("telegramMessages")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .order("desc")
      .take(50);
  },
});

export const getMessageStats = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return {
        total: 0,
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0,
      };
    }

    const messages = await ctx.db
      .query("telegramMessages")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .collect();
    
    const stats = {
      total: messages.length,
      pending: messages.filter(m => m.processingStatus === "pending").length,
      processing: messages.filter(m => m.processingStatus === "processing").length,
      completed: messages.filter(m => m.processingStatus === "completed").length,
      failed: messages.filter(m => m.processingStatus === "failed").length,
    };

    return stats;
  },
});

export const getMessage = internalQuery({
  args: {
    messageId: v.id("telegramMessages"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.messageId);
  },
});

export const updateMessageStatus = internalMutation({
  args: {
    messageId: v.id("telegramMessages"),
    status: v.union(
      v.literal("pending"),
      v.literal("processing"),
      v.literal("completed"),
      v.literal("failed")
    ),
    errorMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.messageId, {
      processingStatus: args.status,
      processed: args.status === "completed",
      errorMessage: args.errorMessage,
    });
  },
});
