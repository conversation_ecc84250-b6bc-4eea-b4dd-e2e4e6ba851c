"use node";

import { internalAction } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";

export const syncTaskToLinear = internalAction({
  args: {
    taskId: v.id("tasks"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    try {
      // Update status to syncing
      await ctx.runMutation(internal.tasks.updateTaskStatus, {
        taskId: args.taskId,
        status: "syncing",
      });

      // Get the task
      const task = await ctx.runQuery(internal.tasks.getTask, {
        taskId: args.taskId,
      });

      if (!task) {
        throw new Error("Task not found");
      }

      // Get user's Linear settings
      const linearApiKey = await ctx.runQuery(internal.settings.getSetting, {
        key: "LINEAR_API_KEY",
        userId: args.userId,
      });
      const linearTeamId = await ctx.runQuery(internal.settings.getSetting, {
        key: "LINEAR_TEAM_ID",
        userId: args.userId,
      });

      if (!linearApiKey || !linearTeamId) {
        throw new Error("Linear API key or team ID not configured for user");
      }

      // Create main task in Linear
      const mainTaskResult = await createLinearTask({
        apiKey: linearApiKey,
        teamId: linearTeamId,
        title: task.title,
        description: task.description,
        priority: mapPriorityToLinear(task.priority),
      });

      // Create subtasks
      const subtaskResults = await Promise.all(
        task.subtasks.map((subtask: any) =>
          createLinearTask({
            apiKey: linearApiKey,
            teamId: linearTeamId,
            title: subtask.title,
            description: subtask.description,
            priority: 0, // Normal priority for subtasks
            parentId: mainTaskResult.id,
          })
        )
      );

      // Update task status to synced
      await ctx.runMutation(internal.tasks.updateTaskStatus, {
        taskId: args.taskId,
        status: "synced",
        linearTaskId: mainTaskResult.id,
        linearUrl: mainTaskResult.url,
      });

      // Notify user via Telegram
      try {
        const taskAfter = await ctx.runQuery(internal.tasks.getTask, { taskId: args.taskId });
        if (taskAfter) {
          const messageDoc = await ctx.runQuery(internal.telegram.getMessage, { messageId: taskAfter.telegramMessageId });
          const botToken = await ctx.runQuery(internal.settings.getSetting, { key: "TELEGRAM_BOT_TOKEN", userId: args.userId });
          if (messageDoc && botToken) {
            const text = `✅ Created Linear issue:\n${mainTaskResult.title}\n${mainTaskResult.url}`;
            await ctx.scheduler.runAfter(0, internal.telegram.sendMessage, {
              botToken,
              chatId: messageDoc.chatId,
              text,
            });
          }
        }
      } catch (notifyErr) {
        console.log("Notify via Telegram failed", notifyErr);
      }

    } catch (error) {
      // Update status to failed
      await ctx.runMutation(internal.tasks.updateTaskStatus, {
        taskId: args.taskId,
        status: "failed",
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });

      // Attempt to notify failure via Telegram
      try {
        const taskDoc = await ctx.runQuery(internal.tasks.getTask, { taskId: args.taskId });
        if (taskDoc) {
          const messageDoc = await ctx.runQuery(internal.telegram.getMessage, { messageId: taskDoc.telegramMessageId });
          const botToken = await ctx.runQuery(internal.settings.getSetting, { key: "TELEGRAM_BOT_TOKEN", userId: args.userId });
          if (messageDoc && botToken) {
            const errText = error instanceof Error ? error.message : "Unknown error";
            const text = `❌ Failed to create Linear task:\n${errText}`;
            await ctx.scheduler.runAfter(0, internal.telegram.sendMessage, {
              botToken,
              chatId: messageDoc.chatId,
              text,
            });
          }
        }
      } catch (notifyErr) {
        console.log("Notify failure via Telegram failed", notifyErr);
      }
    }
  },
});

async function createLinearTask({
  apiKey,
  teamId,
  title,
  description,
  priority,
  parentId,
}: {
  apiKey: string;
  teamId: string;
  title: string;
  description: string;
  priority: number;
  parentId?: string;
}) {
  const mutation = `
    mutation IssueCreate($input: IssueCreateInput!) {
      issueCreate(input: $input) {
        success
        issue {
          id
          identifier
          title
          url
        }
      }
    }
  `;

  const variables = {
    input: {
      teamId,
      title,
      description,
      priority,
      ...(parentId && { parentId }),
    },
  };

  const response = await fetch("https://api.linear.app/graphql", {
    method: "POST",
    headers: {
      "Authorization": `Bearer ${apiKey}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      query: mutation,
      variables,
    }),
  });

  if (!response.ok) {
    throw new Error(`Linear API error: ${response.status}`);
  }

  const data = await response.json();

  if (data.errors) {
    throw new Error(`Linear GraphQL error: ${JSON.stringify(data.errors)}`);
  }

  if (!data.data.issueCreate.success) {
    throw new Error("Failed to create Linear task");
  }

  return data.data.issueCreate.issue;
}

function mapPriorityToLinear(priority: string): number {
  switch (priority) {
    case "urgent": return 1; // Urgent
    case "high": return 2; // High
    case "medium": return 3; // Medium
    case "low": return 4; // Low
    default: return 3; // Medium
  }
}
