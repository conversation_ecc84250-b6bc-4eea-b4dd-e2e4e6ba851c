"use node";

import { internalAction } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";

export const processMessageWithAI = internalAction({
  args: {
    messageId: v.id("telegramMessages"),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    try {
      // Update status to processing
      await ctx.runMutation(internal.telegram.updateMessageStatus, {
        messageId: args.messageId,
        status: "processing",
      });

      // Get the message
      const message = await ctx.runQuery(internal.telegram.getMessage, {
        messageId: args.messageId,
      });

      if (!message) {
        throw new Error("Message not found");
      }

      // Get user's OpenRouter API key
      const openrouterKey = await ctx.runQuery(internal.settings.getSetting, {
        key: "OPENROUTER_API_KEY",
        userId: args.userId,
      });

      if (!openrouterKey) {
        throw new Error("OpenRouter API key not configured for user");
      }

      // Process with OpenRouter GPT
      const taskData = await processWithGPT(message.text, openrouterKey);

      if (taskData) {
        // Create task in database
        const taskId = await ctx.runMutation(internal.tasks.createTask, {
          userId: args.userId,
          telegramMessageId: args.messageId,
          title: taskData.title,
          description: taskData.description,
          priority: taskData.priority,
          subtasks: taskData.subtasks,
        });

        // Schedule Linear sync
        await ctx.scheduler.runAfter(0, internal.linear.syncTaskToLinear, {
          taskId,
          userId: args.userId,
        });
      }

      // Update status to completed
      await ctx.runMutation(internal.telegram.updateMessageStatus, {
        messageId: args.messageId,
        status: "completed",
      });

    } catch (error) {
      // Update status to failed
      await ctx.runMutation(internal.telegram.updateMessageStatus, {
        messageId: args.messageId,
        status: "failed",
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
    }
  },
});

async function processWithGPT(messageText: string, apiKey: string) {
  try {
    const response = await fetch("https://openrouter.ai/api/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${apiKey}`,
        "Content-Type": "application/json",
        "HTTP-Referer": "https://convex.dev",
        "X-Title": "Telegram Linear Integration",
      },
      body: JSON.stringify({
        model: "openai/gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: `You are a task extraction AI. Analyze the given message and extract actionable tasks. 
            
            Return a JSON object with this structure:
            {
              "title": "Main task title (max 100 chars)",
              "description": "Detailed description of the main task",
              "priority": "low|medium|high|urgent",
              "subtasks": [
                {
                  "title": "Subtask title",
                  "description": "Subtask description"
                }
              ]
            }
            
            Rules:
            - Only extract if the message contains actionable items
            - Break complex tasks into logical subtasks
            - Use clear, concise language
            - Set appropriate priority based on urgency indicators
            - Return null if no actionable tasks found
            
            Priority guidelines:
            - urgent: immediate action needed, deadlines today
            - high: important, needs attention this week
            - medium: standard tasks, can be done within 2 weeks
            - low: nice to have, no specific timeline`
          },
          {
            role: "user",
            content: messageText
          }
        ],
        temperature: 0.3,
        max_tokens: 1000,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`);
    }

    const data = await response.json();
    const content = data.choices[0]?.message?.content;

    if (!content) {
      return null;
    }

    try {
      const parsed = JSON.parse(content);
      
      // Validate the structure
      if (parsed && parsed.title && parsed.description && parsed.priority) {
        return {
          title: parsed.title,
          description: parsed.description,
          priority: parsed.priority,
          subtasks: parsed.subtasks || [],
        };
      }
    } catch (parseError) {
      console.error("Failed to parse GPT response:", parseError);
    }

    return null;
  } catch (error) {
    console.error("GPT processing error:", error);
    throw error;
  }
}
