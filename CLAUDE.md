# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Core Architecture

**BuddyTasks** is a Telegram-to-Linear automation app built with Convex as the backend and React+Vite for the frontend. The app receives Telegram messages via webhooks, processes them with AI to extract tasks, and automatically creates Linear issues.

### Technology Stack
- **Backend**: Convex (serverless backend-as-a-service)
- **Frontend**: React + Vite + TypeScript + Tailwind CSS
- **Auth**: Convex Auth with anonymous authentication
- **AI**: OpenAI integration for task extraction
- **Security**: AES-256-GCM encryption, HMAC signatures, rate limiting
- **Integrations**: Telegram Bot API, Linear GraphQL API

## Development Commands

```bash
# Start development servers (frontend + backend)
bun run dev

# Frontend only
bun run dev:frontend

# Backend only  
bun run dev:backend

# Full build and type check
bun run build

# Lint and type check everything
bun run lint

# Deploy to Convex
bun convex deploy
```

## Project Structure

```
/convex/               # Convex backend functions
  ├── schema.ts        # Database schema with security tables
  ├── router.ts        # HTTP routes (webhooks)
  ├── encryption*.ts   # Encryption system (split for Convex requirements)
  ├── security.ts      # Rate limiting, HMAC, security headers
  ├── validation.ts    # Zod schemas for input validation
  ├── settings.ts      # User settings with encryption
  ├── tasks.ts         # Task management
  ├── telegram.ts      # Telegram integration
  ├── linear.ts        # Linear integration
  ├── ai.ts           # OpenAI task extraction
  └── admin*.ts        # Security admin functions

/src/                  # React frontend
  ├── App.tsx          # Main app with tab navigation
  ├── components/      # React components
  └── lib/utils.ts     # Utility functions

/docs/                 # Documentation
  ├── CONVEX_RULES.md       # Convex development guidelines
  ├── SECURITY_SETUP.md     # Security implementation guide  
  └── DEPLOYMENT_CHECKLIST.md  # Production deployment steps
```

## Convex Function Architecture

**CRITICAL**: Convex has strict function separation requirements:

### Function Types & Placement
- **Queries/Mutations**: Regular functions, no `"use node"` directive
- **Actions**: Can use `"use node"` for Node.js APIs (crypto, fetch, etc.)
- **HTTP Routes**: Defined in `router.ts` using `httpAction`

### File Organization Pattern
- `encryption.ts`: Node.js actions only (currently empty)
- `encryptionQueries.ts`: Queries and mutations for encryption
- `admin.ts`: Node.js actions for admin operations
- `adminQueries.ts`: Queries for admin dashboards

### Function Reference System
```typescript
// Public functions
import { api } from "./_generated/api";
await ctx.runQuery(api.settings.getAllSettings, {});

// Internal functions  
import { internal } from "./_generated/api";
await ctx.runQuery(internal.encryptionQueries.decryptSetting, {});
```

## Security Implementation

This app implements enterprise-grade security:

### Encryption System
- **AES-256-GCM** with user-specific keys derived from master key + user ID
- **Automatic encryption** for sensitive settings (API keys, bot tokens)
- **Migration support** from plain text to encrypted storage

### Webhook Security Stack
- **HMAC SHA-256** signature validation
- **Rate limiting** (100 req/min per IP)
- **Input validation** with Zod schemas  
- **Request size limits** (10MB max)
- **Suspicious activity detection** (XSS, SQL injection patterns)

### Access Control
- **User isolation**: All data scoped to authenticated user
- **Row-level security** helpers for resource ownership
- **Restricted API access** for sensitive operations

## Data Flow

1. **Telegram → Webhook**: Telegram sends updates to `/telegram/webhook`
2. **Security Validation**: HMAC signature, rate limits, input validation
3. **Message Processing**: Store message, trigger AI task extraction
4. **AI Analysis**: OpenAI extracts structured task data from messages
5. **Linear Integration**: Create Linear issues with extracted task details
6. **Status Tracking**: Update task status and sync with Linear

## Database Schema

Key tables with security considerations:
- `telegramMessages`: User-scoped message storage
- `tasks`: Extracted tasks with Linear sync status
- `settings`: Encrypted user settings (API keys)
- `securityLogs`: Audit trail for security events
- `webhookLogs`: HTTP request logging

## Environment Variables

Required in Convex dashboard:
```
ENCRYPTION_MASTER_KEY=<32-byte-base64-key>
WEBHOOK_SECRET=<global-hmac-secret>
MAX_REQUEST_SIZE=10485760
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=60000
```

## Development Guidelines

### Convex Function Development
- **Always follow** `docs/CONVEX_RULES.md` for function syntax
- **Use new function syntax** with `args`, `returns`, `handler`
- **Include validators** for all function arguments and returns
- **Separate Node.js actions** from regular queries/mutations

### Security Requirements  
- **Never hardcode** API keys or secrets in source code
- **Always validate** user input with Zod schemas
- **Use encryption helpers** for storing sensitive data
- **Log security events** for audit trails

### Testing Deployment
```bash
# Type check before deployment
npx convex dev

# Test security functions in Convex dashboard
await runQuery("admin:testEncryption", {testData: "test"});
await runQuery("admin:getSystemSecurityStatus", {});
```

## Integration APIs

### Telegram Bot Setup
Webhook URL format (secure):
```
https://your-deployment.convex.site/telegram/webhook?secret=WEBHOOK_SECRET
```

### Linear Integration
Requires `LINEAR_API_KEY` and `LINEAR_TEAM_ID` in encrypted user settings.

### OpenAI Integration  
Requires `OPENROUTER_API_KEY` for task extraction from Telegram messages.

## Error Handling

The app uses comprehensive error tracking:
- **Security logs** for authentication/authorization failures
- **Webhook logs** for HTTP request tracking  
- **Task status tracking** for Linear sync failures
- **Graceful degradation** for external API failures