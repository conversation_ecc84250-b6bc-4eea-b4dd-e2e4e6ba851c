# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Ignored for the template, you probably want to remove it:
package-lock.json

# Security - Environment and sensitive files
.env.local
.env.production
.env.development
.env.test
convex.env
*.pem
*.key
*.crt
*.p12
secrets.json
config/production.json
config/secrets.json

# Convex deployment keys (CRITICAL: never commit these!)
CONVEX_DEPLOY_KEY*
deployment-*.json

# Backup files that might contain sensitive data
*.backup
*.bak
*.tmp
*~

# IDE and editor files that might contain sensitive data
.vscode/settings.json
.idea/dataSources.xml
.idea/dataSources.local.xml
.idea/workspace.xml
.idea/tasks.xml
.idea/dictionaries/