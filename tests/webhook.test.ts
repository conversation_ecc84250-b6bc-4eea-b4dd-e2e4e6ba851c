import { describe, it, expect } from 'vitest';
import { TelegramUpdateSchema, sanitizeText, extractClientIP, validateRequestSize, getWebhookSecretFromRequest } from '../convex/validation';
import { checkRateLimit } from '../convex/security';

describe('TelegramUpdateSchema', () => {
  it('accepts a minimal valid message update', () => {
    const payload = {
      update_id: 1,
      message: {
        message_id: 10,
        date: 1730000000,
        chat: { id: 12345, type: 'private' },
        text: 'hello',
      },
    };
    const res = TelegramUpdateSchema.safeParse(payload);
    expect(res.success).toBe(true);
  });

  it('rejects malformed payload', () => {
    const res = TelegramUpdateSchema.safeParse({ foo: 'bar' });
    expect(res.success).toBe(false);
  });
});

describe('sanitizeText', () => {
  it('removes script tags and dangerous content', () => {
    const dirty = '<script>alert(1)</script>hello javascript:alert(2) onclick="x()"';
    const clean = sanitizeText(dirty);
    expect(clean).not.toMatch(/script/i);
    expect(clean).not.toMatch(/javascript:/i);
    expect(clean).not.toMatch(/onclick/i);
    expect(clean).toMatch(/hello/);
  });
});

describe('extractClientIP', () => {
  it('uses cf-connecting-ip when present', () => {
    const req = new Request('https://example.com', { headers: { 'cf-connecting-ip': '*******' } });
    expect(extractClientIP(req)).toBe('*******');
  });
  it('falls back to x-forwarded-for', () => {
    const req = new Request('https://example.com', { headers: { 'x-forwarded-for': '*******, *******' } });
    expect(extractClientIP(req)).toBe('*******');
  });
});

describe('validateRequestSize', () => {
  it('allows under limit and blocks over limit', () => {
    expect(validateRequestSize('100', 200)).toBe(true);
    expect(validateRequestSize('300', 200)).toBe(false);
  });
});

describe('getWebhookSecretFromRequest', () => {
  it('prefers header over query', () => {
    const req = new Request('https://example.com/telegram/webhook?secret=querysecret', {
      headers: { 'X-Telegram-Bot-Api-Secret-Token': 'headersecret' },
    });
    const { secret, source } = getWebhookSecretFromRequest(req);
    expect(secret).toBe('headersecret');
    expect(source).toBe('header');
  });
  it('reads query param when header missing', () => {
    const req = new Request('https://example.com/telegram/webhook?secret=querysecret');
    const { secret, source } = getWebhookSecretFromRequest(req);
    expect(secret).toBe('querysecret');
    expect(source).toBe('query');
  });
  it('returns null when missing', () => {
    const req = new Request('https://example.com/telegram/webhook');
    const { secret, source } = getWebhookSecretFromRequest(req);
    expect(secret).toBeNull();
    expect(source).toBeNull();
  });
});

describe('checkRateLimit', () => {
  it('limits after exceeding max requests in window', () => {
    const key = `test_${Date.now()}`;
    const max = 3;
    const windowMs = 60_000;
    // First three allowed
    expect(checkRateLimit(key, max, windowMs).allowed).toBe(true);
    expect(checkRateLimit(key, max, windowMs).allowed).toBe(true);
    expect(checkRateLimit(key, max, windowMs).allowed).toBe(true);
    // Fourth denied
    expect(checkRateLimit(key, max, windowMs).allowed).toBe(false);
  });
});
