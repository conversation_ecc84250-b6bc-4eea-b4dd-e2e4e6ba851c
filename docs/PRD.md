Below is a complete, implementation‑ready PRD plus a Convex schema (with indexes) tailored to your stack and to the exact flows you described (Telegram → OpenRouter → Linear). I’ve grounded the key decisions in the official docs and called out the relevant citations inline.

---

## PRD — AI‑Powered Task App (Telegram ↔ OpenRouter ↔ Linear)

### 0) One‑liner

Turn any Telegram message into a well‑formed Linear task using an LLM (via OpenRouter), with secure per‑user key storage, auth via @convex-dev/auth, real‑time status in React 19 + Vite + Convex. Linear OAuth and subscriptions/billing are out of scope for the current stack.

---

### 1) Goals & Non‑Goals

**Goals**

* Let users DM a Telegram bot; the app summarizes/parses intent with OpenRouter and creates issues in Linear.
* Support both **OAuth** (preferred) and **API key** flows for Linear. (OAuth scopes & flow per Linear docs. ([Linear][1]))
* Allow **BYO OpenRouter API key** (per user) and/or organization‑level key; respect model headers and structured output support. ([OpenRouter][2])
* **Strong secret handling**: encrypt all user‑provided credentials at rest at the application level (in addition to Convex’s platform encryption at rest), never expose in logs.
* Auth with **@convex-dev/auth** (Password + Anonymous in this repo). No billing/subscription gating in MVP.
* Convex‑native webhooks via **HTTP Actions** for Telegram and Linear; durable background workflow via **scheduler**. ([Convex Developer Hub][5])
* Admin dashboard (React + Tailwind + shadcn/ui style components) for integration status, message logs, and usage.

---

### Current Implementation Status (Repo Audit)

- Auth: @convex-dev/auth configured (Password + Anonymous). Implemented.
- Telegram Webhook: `convex/http.ts` exposes `/telegram/webhook`, validates `X-Telegram-Bot-Api-Secret-Token` against per-user secret, ACKs immediately, schedules processing. Implemented.
- AI Parsing: `convex/ai.ts` calls OpenRouter and extracts task JSON. Implemented (improve with JSON mode/response_format).
- Linear Create: `convex/linear.ts` creates issues via GraphQL using personal API key + team id. Implemented. OAuth: future.
- Reply to Telegram: Not implemented yet (missing `sendMessage` on success/error).
- Rate limiting & request guards: Utilities exist (`convex/security.ts`, `convex/validation.ts`) but not wired into `/telegram/webhook`. Partial.
- Encryption: Application-level AES-GCM encryption for sensitive settings via `ENCRYPTION_MASTER_KEY`. Implemented.
- Observability: `webhookLogs` and `securityLogs` tables + queries; webhook POSTs not currently logged in `http.ts`. Partial.
- Frontend (React + Vite): OnboardingWizard, Settings (with validation/diagnostics), Dashboard with stats and logs. Implemented.

Immediate fixes and gaps to address

1) Add Telegram `sendMessage` action to confirm created Linear issue(s) back to the chat.
2) Wire rate limiting + request size guard + suspicious activity check into `/telegram/webhook`.
3) Log inbound webhook POSTs via `webhookLogs.logWebhookRequest` for traceability.
4) Use OpenRouter JSON mode/`response_format` where supported; add robust JSON fallback parsing.
5) Optional: capture usage (tokens) from OpenRouter and store with tasks.
6) Make `getConvexSiteUrl` configurable (avoid hard-coded deployment ids).

**Non‑Goals**

* We do not build full Linear project management UI—only issue creation and minimal linking back to Linear.
* No advanced routing brain (ML) beyond rule‑based team/label routing and model prompts.

---

### 2) Personas

* **Individual developer**: wants a fast way to turn chat notes into Linear issues.
* **Team lead/PM**: wants a shared bot, subscription plan, and workspace‑level defaults (team, labels, templates).
* **Ops/Support agent**: triggers a task from Telegram group chats and wants confirmation back in the chat.

---

### 3) High‑level Architecture

**Frontend** (React 19 + Vite)

* Auth via @convex-dev/auth; app tabs: Dashboard, Setup (Onboarding), Settings.
* Settings: save encrypted keys, validate/test providers, generate webhook secret, configure Telegram webhook.
* Real‑time dashboard data via Convex client.

**Backend** (Convex)

* **HTTP Actions** exposed at `https://<deployment>.convex.site/...` for third‑party webhooks (Telegram updates & Linear webhooks). ([Convex Developer Hub][5])
* **Actions** for external calls (OpenRouter, Telegram sendMessage, Linear GraphQL).
* **Mutations/Queries** for DB operations (saving encrypted secrets, message logs, issues).
* **Scheduler** (`runAfter`/`runAt`) to decouple inbound webhook ACK from LLM/Linear work and make the flow resilient. ([Convex Developer Hub][6])

**3rd Parties**

* **Telegram Bot API** for updates (webhook with `secret_token`) and message replies. ([Telegram][7])
* **OpenRouter** Chat Completions API (headers `HTTP-Referer`, `X-Title`; structured outputs when supported). ([OpenRouter][2])
* **Linear** OAuth 2.0 → GraphQL mutations to create issues; webhooks optional for issue echo updates. ([Linear][1])
* (Optional future) Billing/subscriptions; not part of current implementation.

---

### 4) Key Flows

#### 4.1 Telegram → Linear issue

1. **Telegram** sends POST to our Convex HTTP Action `/telegram/webhook`. We verify:

   * `X-Telegram-Bot-Api-Secret-Token` equals the secret we used in `setWebhook`. ([Telegram][7])
   * Basic sanity on update structure.
2. Insert **inbound message** doc (Convex).
3. Respond **200 OK immediately** to Telegram (don’t block).
4. Schedule `processTelegramUpdate` (Convex **scheduler**) with the inbound message *id*. ([Convex Developer Hub][6])
5. In `processTelegramUpdate`:

   * Resolve **user** by telegram user id or chat linking.
   * Decrypt **OpenRouter** key; call `/chat/completions` with a prompt that yields structured JSON for issue fields (title, description, labels, team). (Honor `HTTP-Referer`/`X-Title` headers.) ([OpenRouter][2])
   * If parsing yields multiple tasks, create one issue per task.
   * Acquire **Linear** access (OAuth token or personal key). (OAuth code exchange, token usage per docs.) ([Linear][1])
   * Create issue(s) via Linear GraphQL. (Endpoint `https://api.linear.app/graphql`, Authorization Bearer.) ([Linear][8])
   * Persist **outcome** docs (issue ids, errors, token usage).
   * **Reply** to Telegram via `sendMessage` with the new issue URL(s). ([Telegram][7])

#### 4.2 Connect Linear (OAuth)

* User clicks **Connect Linear** → we redirect user to `https://linear.app/oauth/authorize` with `response_type=code`, `client_id`, `redirect_uri`, `scope`. We prefer `write` and/or `issues:create` minimal scope, `state` for CSRF, and `actor=user` unless organization chooses `actor=app` for agents. After redirect back, exchange `code` for `access_token` at `https://api.linear.app/oauth/token`. Store encrypted token. Support revoke. ([Linear][1])

#### 4.3 Add OpenRouter key (BYO)

* User enters API key; we envelope‑encrypt and store it. For app‑level keys, store at org scope; still encrypted. Use headers recommended by OpenRouter when calling the API. ([OpenRouter][2])

#### 4.4 Link Telegram account / Login with Telegram

* **Option A (Linking via bot deep link)**: user clicks a `t.me/<bot>?start=<nonce>` link; the bot receives `/start` with `nonce`, and we bind Telegram user id ↔ authenticated user in Convex.
* **Option B (Telegram Login Widget)**: optional; if used, verify `hash` with HMAC‑SHA256 using `SHA256(bot_token)` per Telegram’s doc; then bind Telegram id to the Convex user. ([Telegram][9])
* Register webhook with `secret_token` to authenticate origin of Telegram webhooks. ([Telegram][7])

#### 4.5 Subscription gating (Future)

* Not in scope for the MVP. If needed later, implement simple per-user plan flags in Convex or integrate a billing provider.

---

### 5) Security Design

**Platform**

* Convex encrypts all customer data at rest (AES‑256) and uses TLS in transit; SOC2 Type II & HIPAA options available (BAA). This is baseline; we add **application‑level encryption** for user secrets. ([Convex][10])

**Application‑level encryption (envelope)**

* **Master/KEK**: Use an external KMS (AWS KMS/GCP KMS) or a Convex env var that holds a wrapped key. Access only from **Node runtime actions** (for AWS SDK etc.) or WebCrypto where appropriate. (Convex has a default web‑like runtime and an opt‑in Node.js runtime for actions.) ([Convex Developer Hub][11])
* **Data keys**: For each secret, generate a random 256‑bit data key; encrypt secret with AES‑256‑GCM (store `ciphertext`, `iv`, `tag`, `alg`, `version`). Then **wrap** the data key with KEK (or store KMS `ciphertextBlob`).
* **At rest**: Store only ciphertext + wrapped key material in Convex; never store plaintext tokens in DB or logs.
* **In memory**: Decrypt only at call‑time of provider APIs; zeroize buffers after use.
* **Transport**: Only call providers from Convex **actions** (server‑side). No client exposure.

**Webhook verification**

* Telegram: verify `X‑Telegram‑Bot‑Api‑Secret‑Token` equals our secret set via `setWebhook`. ([Telegram][7])
* Linear: verify via a per‑app secret and origin; store webhook delivery logs.
* If a billing provider is added later: verify webhook signatures per provider guidance.

**AuthZ**

* Authorize using @convex-dev/auth: map authenticated user (`getAuthUserId`) to all Convex function access and per-user scoping.

**PII & logging**

* Redact secrets, chat text optional (configurable), store truncated message previews for UX.
* Token usage & costs (OpenRouter response contains usage stats) are stored without prompts unless enabled. ([OpenRouter][2])

**Rate limiting**

* Per user/org on Telegram webhook and OpenRouter calls; exponential backoff on provider 429/5xx.

---

### 6) Functional Requirements (selected)

**Integrations**

* Linear:

  * OAuth connect/disconnect, token refresh handling when needed; create issues via GraphQL; optional webhooks for status echo. ([Linear][1])
* OpenRouter:

  * Models selectable per org or per user; set `HTTP-Referer` and `X-Title` headers; support `response_format` JSON when available; tool/structured outputs where models support it. ([OpenRouter][2])
* Telegram:

  * Webhook (POST), `secret_token`, allowed update types; reply via `sendMessage`. ([Telegram][7])

**Gating**

* Free plan: N messages/day, 1 model, 1 team route.
* Pro: higher quotas, multi‑team routing, attachments.

**Admin**

* Issue routing rules (by chat id, keyword → Linear team/label).
* Mapping: Telegram chat → Linear team; fallback team.

**Observability**

* Message processing timeline (inbound → LLM → Linear → reply).
* Error classification (provider errors, parse errors, auth errors).

---

### 7) Data Model (Convex) — Tables & Indexes

> Notes: Convex stores JSON‑like documents with schema validation via `v`, supports bytes via `v.bytes()`, and indexes are defined in the schema for fast queries. ([Convex Developer Hub][13])

7A) Current schema snapshot (as implemented in this repo)

- `settings`: per-user key/value, `isEncrypted` flag; indexes: `by_user_and_key`, `by_user`.
- `telegramMessages`: per-user inbound messages with `processingStatus` and `processed` flags; indexes: `by_user`, `by_user_and_status`, `by_user_and_processed`.
- `tasks`: per-user tasks linked to `telegramMessageId`, Linear ids/URL, priority, subtasks array; indexes: `by_user`, `by_user_and_status`, `by_user_and_telegram_message`.
- `webhookLogs`: per-user webhook request logs (method, path, body, status, error); indexes: `by_user`, `by_timestamp`, `by_status`.
- `securityLogs`: audit trail (eventType, details, severity, ip/userAgent/url); indexes: `by_timestamp`, `by_event_type`, `by_severity`, `by_user`.

The larger, multi-org schema below is an optional future expansion (not implemented yet).

```ts
// convex/schema.ts
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Organizations & Users
  orgs: defineTable({
    name: v.string(),
    clerkOrganizationId: v.optional(v.string()),
    plan: v.string(), // "free" | "pro" | "enterprise"
    settings: v.object({
      defaultModel: v.optional(v.string()),
      defaultLinearTeamId: v.optional(v.string()),
      allowUserBYOKeys: v.optional(v.boolean()),
    }),
  }).index("by_clerk_org", ["clerkOrganizationId"]),

  users: defineTable({
    clerkUserId: v.string(),
    orgId: v.id("orgs"),
    email: v.optional(v.string()),
    displayName: v.optional(v.string()),
    role: v.string(), // "owner" | "admin" | "member"
  })
    .index("by_clerk_user", ["clerkUserId"])
    .index("by_org", ["orgId"]),

  // Subscriptions & Billing (synced from Clerk Billing webhooks)
  subscriptions: defineTable({
    orgId: v.id("orgs"),
    provider: v.literal("clerk"),
    externalId: v.string(), // Clerk subscription id
    status: v.string(), // "active" | "trialing" | "past_due" | "canceled" ...
    plan: v.string(),
    currentPeriodEnd: v.number(), // ms epoch
  }).index("by_org", ["orgId"]),

  // Integrations & Secrets
  connections: defineTable({
    orgId: v.id("orgs"),
    userId: v.id("users"), // who connected it (may be an admin)
    provider: v.string(), // "linear" | "telegram" | "openrouter"
    type: v.optional(v.string()), // e.g., "oauth" | "api_key" | "bot"
    status: v.string(), // "connected" | "revoked" | "error" | "pending"
    displayName: v.optional(v.string()),
    metadata: v.optional(v.object({})), // provider-specific (teamId, model, etc.)
    secretRefId: v.optional(v.id("secrets")),
  })
    .index("by_org", ["orgId"])
    .index("by_user", ["userId"])
    .index("by_provider", ["provider"]),

  // Encrypted secrets (application-level encryption)
  // Envelope encryption: ciphertext + iv + tag + key wrapping metadata
  secrets: defineTable({
    orgId: v.id("orgs"),
    userId: v.optional(v.id("users")), // null for org-scoped secret
    purpose: v.string(), // "linear.oauth", "linear.key", "openrouter.key", "telegram.bot_token"
    algorithm: v.string(), // "AES-256-GCM"
    ciphertext: v.bytes(), // base64 serialized by client lib
    iv: v.bytes(),
    tag: v.bytes(),
    keyVersion: v.number(), // allow rotation
    wrappedKey: v.bytes(), // KMS ciphertext blob or wrapped DEK
    wrapping: v.object({
      method: v.string(), // "aws-kms" | "local-kek"
      keyId: v.string(),  // KMS KeyId or KEK id/label
    }),
    createdBy: v.id("users"),
    createdAt: v.number(),
    rotatedAt: v.optional(v.number()),
  })
    .index("by_org", ["orgId"])
    .index("by_purpose", ["purpose"])
    .index("by_user", ["userId"]),

  // Telegram
  telegramBots: defineTable({
    orgId: v.id("orgs"),
    name: v.string(),
    username: v.string(), // bot username
    webhookUrl: v.optional(v.string()),
    webhookSecret: v.string(), // used with setWebhook secret_token
    secretRefId: v.id("secrets"), // encrypted bot token
    status: v.string(), // "active" | "disabled"
  }).index("by_org", ["orgId"]),

  telegramAccounts: defineTable({
    orgId: v.id("orgs"),
    userId: v.id("users"),
    telegramUserId: v.string(),
    username: v.optional(v.string()),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    linkedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_telegram_user", ["telegramUserId"]),

  telegramChats: defineTable({
    orgId: v.id("orgs"),
    chatId: v.string(), // group or DM
    type: v.string(), // "private" | "group" | "supergroup" | "channel"
    title: v.optional(v.string()),
    botId: v.id("telegramBots"),
    routing: v.object({
      linearTeamId: v.optional(v.string()),
      defaultLabels: v.optional(v.array(v.string())),
    }),
  })
    .index("by_org", ["orgId"])
    .index("by_chatId", ["chatId"]),

  // Inbound/outbound messages & processing
  inboundMessages: defineTable({
    orgId: v.id("orgs"),
    chatId: v.string(),
    telegramUpdateId: v.string(),
    fromTelegramUserId: v.string(),
    text: v.string(),
    raw: v.optional(v.object({})), // subset/sanitized
    status: v.string(), // "queued" | "processed" | "error"
    error: v.optional(v.string()),
    processedAt: v.optional(v.number()),
  })
    .index("by_org", ["orgId"])
    .index("by_chat", ["chatId"])
    .index("by_update", ["telegramUpdateId"]),

  outboundMessages: defineTable({
    orgId: v.id("orgs"),
    chatId: v.string(),
    telegramMessageId: v.optional(v.string()),
    text: v.string(),
    sentAt: v.optional(v.number()),
    status: v.string(), // "queued" | "sent" | "error"
    error: v.optional(v.string()),
  }).index("by_org", ["orgId"]),

  // AI runs (LLM calls via OpenRouter)
  aiRuns: defineTable({
    orgId: v.id("orgs"),
    userId: v.optional(v.id("users")),
    inboundMessageId: v.optional(v.id("inboundMessages")),
    model: v.string(),
    promptPreview: v.optional(v.string()),
    responsePreview: v.optional(v.string()),
    usage: v.optional(
      v.object({ prompt_tokens: v.number(), completion_tokens: v.number(), total_tokens: v.number() })
    ),
    status: v.string(), // "succeeded" | "failed"
    error: v.optional(v.string()),
    createdAt: v.number(),
  })
    .index("by_org", ["orgId"])
    .index("by_message", ["inboundMessageId"]),

  // Linear issues mapping
  issues: defineTable({
    orgId: v.id("orgs"),
    inboundMessageId: v.id("inboundMessages"),
    linearIssueId: v.string(),
    linearUrl: v.string(),
    teamId: v.optional(v.string()),
    title: v.string(),
    status: v.string(), // "created" | "error"
    error: v.optional(v.string()),
    createdAt: v.number(),
  })
    .index("by_org", ["orgId"])
    .index("by_inbound", ["inboundMessageId"])
    .index("by_linear", ["linearIssueId"]),

  // Routing rules (admin configurable)
  routingRules: defineTable({
    orgId: v.id("orgs"),
    name: v.string(),
    isActive: v.boolean(),
    match: v.object({
      chatId: v.optional(v.string()),
      includes: v.optional(v.array(v.string())), // keywords
      regex: v.optional(v.string()),
    }),
    action: v.object({
      linearTeamId: v.optional(v.string()),
      labels: v.optional(v.array(v.string())),
      template: v.optional(v.object({ title: v.string(), description: v.string() })),
    }),
    priority: v.number(),
  })
    .index("by_org", ["orgId"])
    .index("by_active_priority", ["isActive", "priority"]),

  // Provider/webhook logs
  webhookEvents: defineTable({
    source: v.string(), // "telegram" | "linear" | "clerk"
    orgId: v.optional(v.id("orgs")),
    receivedAt: v.number(),
    headers: v.optional(v.object({})),
    body: v.optional(v.object({})),
    handled: v.boolean(),
    error: v.optional(v.string()),
  }).index("by_source", ["source"]),
});
```

---

### 8) Convex Function Surface (outline)

**HTTP Actions** (public endpoints) — served from `convex/http.ts`. (Convex HTTP Actions and router are documented here. ([Convex Developer Hub][5]))

* `POST /telegram/webhook` → `telegram.webhook` (verify `secret_token`, store inbound, schedule `process`).
* `POST /linear/webhook` → optional (if you subscribe to Linear webhooks for status).
* (Future) Billing webhooks if/when a provider is added.

**Actions** (external IO; some in Node runtime)

* `integrations/openrouter.chatComplete` (decrypt key → POST to OpenRouter with headers per docs). ([OpenRouter][2])
* `integrations/linear.createIssue` (decrypt token → POST GraphQL to Linear). ([Linear][8])
* `integrations/telegram.sendMessage` (decrypt bot token → send). ([Telegram][7])
* `secrets.encrypt`, `secrets.decrypt` (envelope encryption routines; optionally using Node runtime for KMS).
* `oauth/linear.exchangeCode` (code→token exchange). ([Linear][1])

**Mutations/Queries**

* `routing.applyRules` — compute team/labels from message content and chat.
* `keys.saveUserKey` — stores BYO OpenRouter or Linear API key; encrypts and writes to `secrets` & `connections`.
* `connections.connectTelegramBot` — create bot, store webhook secret, set Telegram webhook (with `secret_token`). ([Telegram][7])
* `telegram.processMessage` — main orchestration: call LLM, create issue(s), send confirmations.
* (Future) `billing.upsertSubscription` — only if billing is introduced.

**Scheduling**

* Use `ctx.scheduler.runAfter(0, internal.telegram.processMessage, { inboundMessageId })` to decouple webhook ACK from processing. ([Convex Developer Hub][6])

---

### 9) API/Provider Specifics (grounded)

* **Telegram**

  * Set webhook via `setWebhook` with `secret_token`; Telegram will include `X‑Telegram‑Bot‑Api‑Secret‑Token` on every request. Validate equality and reject if absent/mismatched. ([Telegram][7])
  * Use `sendMessage` to reply with issue links. ([Telegram][7])

* **OpenRouter**

  * Endpoint: `POST https://openrouter.ai/api/v1/chat/completions`
  * Headers: `Authorization: Bearer <API_KEY>`, plus optional `HTTP-Referer`, `X-Title` for app attribution. Support `response_format` where available and tools/structured output. ([OpenRouter][2])

* **Linear**

  * OAuth: `GET https://linear.app/oauth/authorize` (`response_type=code`, `state`, `scope`, `actor`), then `POST https://api.linear.app/oauth/token` (form‑encoded) → store access token (encrypted). Use token as `Authorization: Bearer`. Revoke via `/oauth/revoke`. ([Linear][1])
  * API: GraphQL endpoint `https://api.linear.app/graphql` (create issue via mutation). ([Linear][8])

* (Removed) Clerk Billing — not applicable to this stack.

---

### 10) Acceptance Criteria (samples)

* Messaging:

  * Given a connected Telegram bot and a linked user, when the user sends a message “Create bug: crash on login”, then within one processing cycle the app creates a Linear issue with parsed title/description and replies in Telegram with the issue URL.
* OAuth (Future):

  * When implemented, redirect to Linear OAuth, request minimal scopes, and store a working token on success. ([Linear][1])
* Security:

  * No plaintext secrets in DB or logs; envelope encryption fields present in `secrets`.
  * Telegram webhook requests with invalid `X‑Telegram‑Bot‑Api‑Secret‑Token` are rejected. ([Telegram][7])
* Billing (Future): If a billing provider is added, reflect plan changes and gate features accordingly.

---

### 11) Rate Limits & Error Handling

* Telegram webhooks: acknowledge within 1‑2s; if LLM is slow, we send a follow‑up message with results.
* OpenRouter: on 5xx or timeouts, retry up to N times; use model/provider routing options if desired. ([OpenRouter][2])
* Linear: handle 401 (revoked token) → mark connection “error” and prompt re‑auth.

---

### 12) Observability & Admin

* Dashboard tiles: active connections, message throughput, issue creation success rate, token usage (OpenRouter `usage`), top chats.
* Logs: `webhookEvents`, `aiRuns`, `issues` tables; downloadable CSV.

---

### 13) Deployment & Config

* Frontend: React + Vite (this repo). Host as a static site (e.g., Vercel/Netlify) pointing to Convex for data.
* Convex: functions & HTTP Actions (URLs end with `.convex.site`). ([Convex Developer Hub][5])
* Env vars in Convex for app‑level secrets (e.g., `ENCRYPTION_MASTER_KEY`, allowed origins), set via dashboard/CLI. ([Convex Developer Hub][14])

---

## Convex: encryption approach (practical)

* **Envelope encryption** inside an **Action**:

  1. Generate random data key (`crypto.getRandomValues` or Node `crypto`) and IV.
  2. Encrypt the secret with AES‑GCM (`crypto.subtle` in web‑like runtime, or Node `crypto`), store `ciphertext/iv/tag`. (Convex’s default runtime is web‑like; actions can also run in Node.) ([Convex Developer Hub][11])
  3. Wrap data key with KEK (KMS or local KEK) and store the wrapped form (`wrappedKey`, `keyVersion`).
  4. Decrypt only at call time, never return plaintext to client.

> Convex supports `v.bytes()` for storing raw bytes (base64‑encoded when serialized), ideal for ciphertext/iv/tag. ([Convex Developer Hub][15])

---

## Suggested Convex Files (outline)

* `convex/http.ts`: HTTP router for `/telegram/webhook` (optional: `/linear/webhook`). ([Convex Developer Hub][5])
* `convex/telegram.ts`: `webhook` (httpAction), `processMessage` (internal action), `sendMessage` (action).
* `convex/linear.ts`: `exchangeCode` (action), `createIssue` (action), `revoke` (action).
* `convex/openrouter.ts`: `chatComplete` (action).
* `convex/keys.ts`: `encrypt`, `decrypt`, `saveSecret`, `getSecretForProvider`.
* `convex/routing.ts`: `applyRules`.
* (Future) `convex/billing.ts`: `handleWebhook`, `upsertSubscription`.
* `convex/schema.ts`: (provided above).

---

## React Views (high‑level)

* Dashboard: connection status, recent messages/tasks, webhook logs, stats.
* Setup (Onboarding): guided steps to add keys, generate secret, set webhook.
* Settings: secure key storage, validation/diagnostics, webhook tooling.

---

## Prompts & LLM contract (OpenRouter)

* System prompt guides the model to output a **strict JSON** schema: `{title, description, labels[], teamHint?}`; use `response_format` JSON where model supports. (OpenRouter notes supported providers; structured outputs may depend on model.) ([OpenRouter][2])
* Post‑validate JSON; fall back to a safe title & paste full user message if parse fails.

---

## Testing & QA

* **Integration tests** (Playwright) for OAuth callback and dashboard gating.
* **Provider stubs** for Telegram/Linear (mock HTTP Actions).
* **Repro** for re‑auth path on Linear 401.

---

## Open Items / Decisions (reasonable defaults chosen here)

* **KMS choice**: AWS KMS vs local KEK. (Default to AWS KMS in a Node runtime action.)
* **Actor mode**: default `actor=user` for Linear; `actor=app` feasible for agent‑like flows. ([Linear][1])
* **Chat parsing**: Keyword routing + model inference; start simple, add per‑team templates later.

---

# Appendix A — Convex indexes & query guidance

Use `.withIndex()` instead of `.filter()` for performance where possible, and define composite indexes for your hot paths (e.g., `inboundMessages.by_update`, `connections.by_provider`, `routingRules.by_active_priority`). ([Convex Developer Hub][16])

---

# Appendix B — Example provider calls (references)

* **Telegram setWebhook** with `secret_token` (and verify incoming header). ([Telegram][7])
* **OpenRouter** sample request with `HTTP-Referer`/`X-Title` headers. ([OpenRouter][2])
* **Linear OAuth** authorize + token exchange details and scopes. ([Linear][1])

---

## Why these choices are safe/standard

* Convex HTTP Actions are designed for webhooks and give you a clean public URL (`.convex.site`). ([Convex Developer Hub][5])
* Convex encrypts at rest already; we add application‑level envelope encryption for user secrets to minimize plaintext exposure. ([Convex][10])
* Convex HTTP Actions provide clean public URLs for webhooks. ([Convex Developer Hub][5])
* Telegram’s `secret_token` header and Login Widget HMAC verification are the official, documented ways to authenticate webhook requests and logins. ([Telegram][7])
* Linear’s OAuth and GraphQL endpoints are used per their docs; we request minimal scopes. ([Linear][1])
* OpenRouter headers and structured output recommendations come straight from their API reference. ([OpenRouter][2])

---

### Next steps (implementation order)

1. Harden `/telegram/webhook`: add rate limiting, request guards, and webhook logging.
2. Add Telegram `sendMessage` action to confirm created issues back to the chat.
3. Improve OpenRouter call: enable JSON mode/`response_format` and resilient parsing; store token usage.
4. Optional: routing rules (chat → team/labels); admin UI tiles for processing timeline.
5. Optional: Linear OAuth; optional billing integration.

If you’d like, I can follow up with example Convex actions (`http.ts`, `telegram.webhook`, `openrouter.chatComplete`, `linear.createIssue`) and a minimal Next.js App Router wiring that matches this PRD.

[1]: https://linear.app/developers/oauth-2-0-authentication "OAuth 2.0 authentication – Linear Developers"
[2]: https://openrouter.ai/docs/api-reference/overview "OpenRouter API Reference | Complete API Documentation | OpenRouter | Documentation"
[3]: https://docs.convex.dev/auth/clerk "Convex & Clerk | Convex Developer Hub"
[4]: https://clerk.com/docs/billing/overview "Clerk billing"
[5]: https://docs.convex.dev/functions/http-actions "HTTP Actions | Convex Developer Hub"
[6]: https://docs.convex.dev/scheduling/scheduled-functions "Scheduled Functions | Convex Developer Hub"
[7]: https://core.telegram.org/bots/api "Telegram Bot API"
[8]: https://linear.app/developers/graphql?utm_source=chatgpt.com "Getting started – Linear Developers"
[9]: https://core.telegram.org/widgets/login "Telegram Login Widget"
[10]: https://www.convex.dev/security "Platform Security"
[11]: https://docs.convex.dev/functions/runtimes?utm_source=chatgpt.com "Runtimes | Convex Developer Hub"
[12]: https://clerk.com/docs/billing/events-webhooks "Clerk | Authentication and User Management"
[13]: https://docs.convex.dev/database "Database | Convex Developer Hub"
[14]: https://docs.convex.dev/production/environment-variables "Environment Variables | Convex Developer Hub"
[15]: https://docs.convex.dev/database/types "Data Types | Convex Developer Hub"
[16]: https://docs.convex.dev/database/reading-data/indexes/?utm_source=chatgpt.com "Indexes | Convex Developer Hub"
