# 🚀 Secure Deployment Checklist for BuddyTasks

## ⚠️ PRE-DEPLOYMENT CRITICAL STEPS

### 1. Environment Variables (Convex Dashboard)
- [ ] **ENCRYPTION_MASTER_KEY**: 32-byte base64 key for data encryption
- [ ] **WEBHOOK_SECRET**: Global webhook secret for HMAC validation
- [ ] **MAX_REQUEST_SIZE**: Set to `10485760` (10MB)
- [ ] **RATE_LIMIT_MAX_REQUESTS**: Set to `100`
- [ ] **RATE_LIMIT_WINDOW_MS**: Set to `60000`

Generate encryption key:
```bash
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
```

Generate webhook secret:
```bash
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

### 2. Remove Sensitive Files from Repository
- [ ] Ensure `.env.local` is in `.gitignore` and not committed
- [ ] Remove any hardcoded API keys from code
- [ ] Verify no deployment keys are committed
- [ ] Check for any backup files with sensitive data

### 3. Dependencies
- [ ] Install all required packages: `npm install`
- [ ] Ensure `zod` is listed in dependencies
- [ ] Update packages to latest secure versions

## 🔒 SECURITY DEPLOYMENT

### 1. Deploy Schema Changes
```bash
npx convex deploy
```
- [ ] Verify `securityLogs` table is created
- [ ] Check all indexes are properly created
- [ ] Confirm schema deployment succeeded

### 2. Test Encryption System
In Convex dashboard, run:
```javascript
// Test encryption functionality
await runQuery("admin:testEncryption", {
  testData: "test_sensitive_data_123"
});
```
- [ ] Encryption test passes
- [ ] Verify `encryptionEnabled: true`
- [ ] Check no errors in logs

### 3. Migrate Existing Data
If you have existing plain text settings:
```javascript
// Migrate all plain text settings to encrypted
await runMutation("encryption:migrateAllSettingsToEncrypted", {});
```
- [ ] All existing API keys are encrypted
- [ ] Migration completed without errors
- [ ] Verify settings are still accessible

### 4. Update User Webhook URLs
For each user, update their Telegram webhook URL from:
```
OLD: https://your-deployment.convex.site/telegram/webhook?token=BOT_TOKEN
NEW: https://your-deployment.convex.site/telegram/webhook?secret=WEBHOOK_SECRET
```

Steps for users:
1. Generate webhook secret: `await runMutation("admin:generateWebhookSecret", {})`
2. Update webhook URL in Telegram bot settings
3. Test webhook functionality

## 🧪 TESTING PHASE

### 1. Security Tests
- [ ] **Rate Limiting**: Send 110+ requests rapidly to webhook endpoint
- [ ] **Input Validation**: Send malformed JSON to webhook
- [ ] **Authentication**: Test webhook with invalid secret
- [ ] **XSS Prevention**: Send script tags in message text
- [ ] **Size Limits**: Send request larger than 10MB

### 2. Functional Tests
- [ ] **Encryption**: Test setting and retrieving encrypted API keys
- [ ] **Webhooks**: Verify Telegram messages are processed
- [ ] **Linear Integration**: Test task creation with encrypted keys
- [ ] **OpenRouter**: Test AI processing with encrypted key

### 3. Monitoring Tests
- [ ] **Security Logs**: Verify events are logged correctly
- [ ] **Dashboard**: Check security stats are displayed
- [ ] **Alerts**: Test suspicious activity detection

## 📊 MONITORING SETUP

### 1. Security Dashboard
Access via:
```javascript
// Get security overview
await runQuery("admin:getSystemSecurityStatus", {});

// View recent security events
await runQuery("admin:getUserSecurityEvents", { limit: 50 });
```

### 2. Regular Monitoring Tasks
- [ ] Set up daily security log review
- [ ] Monitor rate limiting events
- [ ] Check for failed authentication attempts
- [ ] Review encryption status regularly

### 3. Alerting (Manual Process)
- [ ] Check critical security events daily
- [ ] Monitor unusual activity patterns
- [ ] Review failed webhook attempts
- [ ] Watch for suspicious IP addresses

## 🔧 POST-DEPLOYMENT VERIFICATION

### 1. Immediate Checks (First Hour)
- [ ] All webhook endpoints responding correctly
- [ ] No critical errors in Convex logs
- [ ] Encryption working for new settings
- [ ] Rate limiting functioning properly
- [ ] Security headers present in responses

### 2. 24-Hour Checks
- [ ] No security events with "critical" severity
- [ ] Webhook processing success rate >95%
- [ ] All encrypted data accessible
- [ ] Performance impact minimal

### 3. Weekly Checks
- [ ] Review security event trends
- [ ] Update dependencies if needed
- [ ] Verify backups include encrypted data
- [ ] Check for new security recommendations

## 🆘 ROLLBACK PLAN

If critical issues are discovered:

### 1. Emergency Rollback
```bash
# Revert to previous deployment
npx convex rollback
```

### 2. Data Recovery
- [ ] Verify encrypted data can be decrypted
- [ ] Check backup data integrity
- [ ] Restore webhook functionality
- [ ] Validate user access

### 3. Communication
- [ ] Notify affected users
- [ ] Document issues encountered
- [ ] Plan fixes for next deployment

## 📋 PRODUCTION HARDENING

### 1. Additional Security Measures
- [ ] Implement IP allowlisting if applicable
- [ ] Set up monitoring alerts
- [ ] Enable additional logging if needed
- [ ] Review and tighten CSP if possible

### 2. Performance Optimization
- [ ] Monitor encryption overhead
- [ ] Optimize database queries
- [ ] Check rate limiting efficiency
- [ ] Review resource usage

### 3. Documentation Updates
- [ ] Update user documentation
- [ ] Document new security features
- [ ] Create incident response procedures
- [ ] Update development guidelines

## ✅ FINAL SIGN-OFF

Before marking deployment complete:
- [ ] All checklist items verified
- [ ] Security team approval (if applicable)
- [ ] User acceptance testing passed
- [ ] Performance benchmarks met
- [ ] Monitoring systems active
- [ ] Documentation updated
- [ ] Team trained on new features

## 🔄 ONGOING MAINTENANCE

### Monthly Tasks
- [ ] Review security logs
- [ ] Update dependencies
- [ ] Rotate encryption keys (quarterly)
- [ ] Security audit (annual)

### Incident Response
- [ ] Document security incident procedures
- [ ] Test incident response plan
- [ ] Maintain emergency contact list
- [ ] Regular team training

---

**Deployment Approved By**: _________________  
**Date**: _________________  
**Security Review**: _________________

Remember: Security is an ongoing process, not a one-time setup!