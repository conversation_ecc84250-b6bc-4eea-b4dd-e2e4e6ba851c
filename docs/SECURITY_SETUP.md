# 🔒 Security Setup Guide for BuddyTasks

## Critical Environment Variables

### 1. Generate Encryption Master Key

The encryption master key is used to encrypt all sensitive data in the database.

```bash
# Generate a secure 32-byte encryption key
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
```

Set this in your Convex dashboard as:
```
ENCRYPTION_MASTER_KEY=<your_generated_key>
```

### 2. Generate Webhook Secrets

Each user should have their own webhook secret instead of exposing bot tokens in URLs.

```bash
# Generate a secure webhook secret
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

Users should set this as `TELEGRAM_WEBHOOK_SECRET` in their settings.

### 3. Required Convex Environment Variables

Set these in your Convex dashboard (Settings > Environment Variables):

```
ENCRYPTION_MASTER_KEY=<32-byte-base64-encoded-key>
WEBHOOK_SECRET=<global-webhook-secret-for-hmac>
MAX_REQUEST_SIZE=10485760
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=60000
```

## Migration Process

### 1. Install Dependencies

```bash
npm install zod
```

### 2. Deploy New Schema

```bash
npx convex deploy
```

This will create the new `securityLogs` table.

### 3. Migrate Existing Settings

Run this in the Convex dashboard console to migrate existing plain text API keys:

```javascript
// Migrate all existing settings to encrypted format
await runMutation("encryption:migrateAllSettingsToEncrypted", {});
```

### 4. Update Webhook URLs

Change your Telegram webhook URLs from:
```
https://your-deployment.convex.site/telegram/webhook?token=YOUR_BOT_TOKEN
```

To:
```
https://your-deployment.convex.site/telegram/webhook?secret=YOUR_WEBHOOK_SECRET
```

## Security Features Implemented

### ✅ Encryption
- **AES-256-GCM** encryption for all sensitive API keys
- **User-specific encryption** with PBKDF2 key derivation
- **Automatic migration** from plain text to encrypted storage

### ✅ Webhook Security
- **HMAC signature validation** for webhook authenticity
- **Rate limiting** (100 requests/minute per IP)
- **Request size limits** (10MB maximum)
- **Input validation** with Zod schemas
- **Suspicious activity detection** 

### ✅ Access Control
- **Row-level security** helpers
- **Restricted API access** for sensitive settings
- **User isolation** - users can only access their own data

### ✅ Monitoring & Logging
- **Security event logging** with severity levels
- **Audit trail** for all sensitive operations
- **Suspicious activity detection**
- **Rate limiting alerts**

### ✅ Data Protection
- **Input sanitization** to prevent XSS
- **Output masking** for sensitive data in logs
- **Secure random token generation**
- **Replay attack prevention**

## Security Headers

All HTTP responses include comprehensive security headers:
- **Content Security Policy (CSP)**
- **Strict Transport Security (HSTS)**
- **X-Content-Type-Options**: `nosniff`
- **X-XSS-Protection**: `1; mode=block`
- **X-Frame-Options**: `DENY`
- **Referrer-Policy**: `strict-origin-when-cross-origin`

## Monitoring Dashboard

View security events in the Convex dashboard:

```javascript
// Get recent security events
await runQuery("security:getRecentSecurityEvents", {
  severity: "high",
  limit: 50
});

// Get events for a specific user
await runQuery("security:getRecentSecurityEvents", {
  userId: "USER_ID",
  eventType: "auth_failure"
});
```

## Testing Security

### 1. Test Encryption

```javascript
// Test encryption/decryption
await runQuery("encryption:testEncryption", {
  userId: "USER_ID",
  testData: "sensitive_api_key_12345"
});
```

### 2. Test Rate Limiting

```bash
# Test rate limiting with multiple requests
for i in {1..110}; do
  curl -X POST https://your-deployment.convex.site/telegram/webhook?secret=test
done
```

### 3. Test Webhook Security

```bash
# Test with invalid secret
curl -X POST https://your-deployment.convex.site/telegram/webhook?secret=invalid \
  -H "Content-Type: application/json" \
  -d '{"update_id": 1, "message": {"message_id": 1, "date": 1234567890, "chat": {"id": 123, "type": "private"}, "text": "test"}}'
```

## Backup & Recovery

### Encryption Key Rotation

1. Generate a new encryption key
2. Set `ENCRYPTION_MASTER_KEY_NEW` environment variable
3. Run migration script to re-encrypt with new key
4. Update `ENCRYPTION_MASTER_KEY` to new value
5. Remove `ENCRYPTION_MASTER_KEY_NEW`

### Database Backup

Ensure regular backups are enabled in Convex dashboard. Encrypted data in backups remains encrypted.

## Security Best Practices

### ✅ Development
- Never commit `.env.local` files
- Use different encryption keys for dev/staging/prod
- Regularly rotate API keys and secrets
- Monitor security logs daily

### ✅ Production
- Enable HTTPS only
- Set up alerting for security events
- Regular security audits
- Keep dependencies updated

### ✅ User Education
- Explain webhook secret setup
- Provide clear security guidelines
- Regular security reminders

## Emergency Response

### Compromised API Key
1. Immediately rotate the compromised key in external service
2. Update encrypted value in BuddyTasks settings
3. Check security logs for unauthorized access
4. Notify affected users if necessary

### Suspicious Activity
1. Check security logs for patterns
2. Block suspicious IPs if needed
3. Increase rate limiting temporarily
4. Review and update detection rules

### Data Breach
1. Immediately rotate encryption master key
2. Audit all access logs
3. Notify users and authorities as required
4. Implement additional security measures

## Compliance Notes

- **GDPR**: User data is encrypted and can be deleted on request
- **SOC 2**: Comprehensive audit logging and access controls
- **ISO 27001**: Risk-based security controls implemented
- **OWASP Top 10**: All major vulnerabilities addressed

## Support

For security issues, contact: <EMAIL>
For implementation help, check the Convex documentation or Discord community.